<template>
  <NuxtLayout>
    <NuxtPage />
  </NuxtLayout>
</template>

<script setup>
const localeHead = useLocaleHead()

// Preload critical resources
useHead({
  htmlAttrs: {
    lang: localeHead.value.htmlAttrs.lang,
    dir: localeHead.value.htmlAttrs.dir
  },
  link: [
    ...localeHead.value.link,
    // Preload critical resources
    { rel: 'preload', href: '/assets/css/main.css', as: 'style' },
    { rel: 'dns-prefetch', href: 'https://click.pageview.click' },
    { rel: 'preconnect', href: 'https://click.pageview.click', crossorigin: '' }
  ],
  meta: localeHead.value.meta
})

// Performance monitoring, PWA and analytics initialization on client
if (process.client) {
  const { getWebVitals } = usePerformanceMonitoring()
  const { initPWA } = usePWA()
  const { initAnalytics, requestAnalyticsConsent } = useAnalytics()
  
  onMounted(() => {
    // Monitor Core Web Vitals
    nextTick(() => {
      const vitals = getWebVitals()
      if (vitals && import.meta.env.DEV) {
        console.log('Web Vitals:', vitals)
      }
    })
    
    // Initialize PWA functionality
    initPWA()
    
    // Initialize analytics with consent
    if (requestAnalyticsConsent()) {
      initAnalytics()
    }
  })
}
</script>