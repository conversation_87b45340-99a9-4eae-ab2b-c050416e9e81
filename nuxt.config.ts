import tailwindcss from "@tailwindcss/vite";
import locales from './i18n/locales'

export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: {
    enabled: true
  },
  css: [
    '~/assets/css/main.css'
  ],
  modules: [
    '@nuxtjs/i18n'
  ],
  vite: {
    plugins: [
      tailwindcss(),
    ],
  },
  i18n: {
    defaultLocale: 'en',
    locales,
    strategy: 'prefix_except_default',
    baseUrl: 'https://convert.land/'
  },
  app: {
    head: {
      title: 'Convert.land - Free Online Unit Converter',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'description', content: 'Free online unit converter for length, weight, temperature, area, volume, time, speed, power, energy, pressure, and data storage. Fast, accurate, and easy to use.' },
        { name: 'keywords', content: 'unit converter, conversion calculator, metric converter, imperial converter, length converter, weight converter, temperature converter' },
        { property: 'og:title', content: 'Convert.land - Free Online Unit Converter' },
        { property: 'og:description', content: 'Free online unit converter for length, weight, temperature, area, volume, time, speed, power, energy, pressure, and data storage.' },
        { property: 'og:type', content: 'website' },
        { property: 'og:url', content: 'https://convert.land/' },
        { name: 'twitter:card', content: 'summary_large_image' },
        { name: 'twitter:title', content: 'Convert.land - Free Online Unit Converter' },
        { name: 'twitter:description', content: 'Free online unit converter for length, weight, temperature, area, volume, time, speed, power, energy, pressure, and data storage.' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        { rel: 'canonical', href: 'https://convert.land/' }
      ]
    }
  },
  nitro: {
    prerender: {
      routes: ['/sitemap.xml']
    }
  }
})
