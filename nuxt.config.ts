import tailwindcss from "@tailwindcss/vite";
import locales from './i18n/locales'

export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: {
    enabled: true
  },
  modules: [
    '@nuxtjs/i18n'
  ],
  vite: {
    plugins: [
      tailwindcss(),
    ],
    build: {
      cssCodeSplit: true,
      rollupOptions: {
        output: {
          manualChunks: {
            'converter-utils': ['~/utils/converter'],
            'vue-vendor': ['vue', 'vue-router']
          }
        }
      }
    }
  },
  experimental: {
    payloadExtraction: false,
    viewTransition: true
  },
  css: [
    '~/assets/css/main.css'
  ],
  i18n: {
    defaultLocale: 'en',
    locales,
    strategy: 'prefix_except_default',
    baseUrl: 'https://convert.land/',
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root'
    }
  },
  app: {
    head: {
      title: 'Convert.land - Free Online Unit Converter | Fast & Accurate Conversions',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'description', content: 'Free online unit converter for length, weight, temperature, area, volume, time, speed, power, energy, pressure, and data storage. Convert between 100+ units instantly. Fast, accurate, and easy to use.' },
        { name: 'keywords', content: 'unit converter, conversion calculator, metric converter, imperial converter, length converter, weight converter, temperature converter, meters to feet, kg to pounds, celsius to fahrenheit, online converter, free converter' },
        { name: 'author', content: 'Convert.land' },
        { name: 'robots', content: 'index, follow' },
        { name: 'language', content: 'en' },
        { name: 'geo.region', content: 'US' },
        { name: 'geo.placename', content: 'United States' },
        { name: 'theme-color', content: '#3B82F6' },
        { property: 'og:title', content: 'Convert.land - Free Online Unit Converter | Fast & Accurate Conversions' },
        { property: 'og:description', content: 'Free online unit converter for length, weight, temperature, area, volume, time, speed, power, energy, pressure, and data storage. Convert between 100+ units instantly.' },
        { property: 'og:type', content: 'website' },
        { property: 'og:url', content: 'https://convert.land/' },
        { property: 'og:site_name', content: 'Convert.land' },
        { property: 'og:locale', content: 'en_US' },
        { property: 'og:image', content: 'https://convert.land/og-image.png' },
        { property: 'og:image:width', content: '1200' },
        { property: 'og:image:height', content: '630' },
        { property: 'og:image:alt', content: 'Convert.land - Free Online Unit Converter' },
        { name: 'twitter:card', content: 'summary_large_image' },
        { name: 'twitter:site', content: '@convertland' },
        { name: 'twitter:creator', content: '@convertland' },
        { name: 'twitter:title', content: 'Convert.land - Free Online Unit Converter' },
        { name: 'twitter:description', content: 'Free online unit converter for length, weight, temperature, area, volume, time, speed, power, energy, pressure, and data storage.' },
        { name: 'twitter:image', content: 'https://convert.land/twitter-image.png' },
        { name: 'twitter:image:alt', content: 'Convert.land - Free Online Unit Converter' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        { rel: 'canonical', href: 'https://convert.land/' },
        { rel: 'manifest', href: '/manifest.json' },
        { rel: 'apple-touch-icon', href: '/icons/icon-192x192.png' }
      ],
      // <script defer data-domain="convert.land" src="https://click.pageview.click/js/script.js"></script>
      script: [
        { src: 'https://click.pageview.click/js/script.js', defer: true, 'data-domain': 'convert.land' }
      ]
    }
  },
  nitro: {
    compressPublicAssets: true,
    minify: true,
    prerender: {
      routes: [
        '/sitemap.xml',
        '/meters-to-feet',
        '/feet-to-meters', 
        '/inches-to-cm',
        '/cm-to-inches',
        '/kg-to-pounds',
        '/pounds-to-kg',
        '/celsius-to-fahrenheit',
        '/fahrenheit-to-celsius'
      ]
    }
  }
})
