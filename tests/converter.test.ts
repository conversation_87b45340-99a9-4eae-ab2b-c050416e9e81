import { describe, it, expect } from 'vitest'
import { 
  convertUnit, 
  formatResult, 
  getUnitSymbol, 
  getUnitName, 
  getCategoryByUnit,
  getPopularConversions,
  unitCategories 
} from '~/utils/converter'

describe('Unit Converter', () => {
  describe('convertUnit', () => {
    it('should convert length units correctly', () => {
      // Feet to meters
      const result1 = convertUnit(1, 'ft', 'm')
      expect(result1.result).toBeCloseTo(0.3048, 4)
      expect(result1.fromUnit).toBe('ft')
      expect(result1.toUnit).toBe('m')

      // Inches to centimeters
      const result2 = convertUnit(1, 'in', 'cm')
      expect(result2.result).toBeCloseTo(2.54, 2)

      // Kilometers to miles
      const result3 = convertUnit(1, 'km', 'mi')
      expect(result3.result).toBeCloseTo(0.621371, 4)
    })

    it('should convert mass units correctly', () => {
      // Pounds to kilograms
      const result1 = convertUnit(1, 'lb', 'kg')
      expect(result1.result).toBeCloseTo(0.453592, 4)

      // Kilograms to pounds
      const result2 = convertUnit(1, 'kg', 'lb')
      expect(result2.result).toBeCloseTo(2.20462, 4)

      // Ounces to grams
      const result3 = convertUnit(1, 'oz', 'g')
      expect(result3.result).toBeCloseTo(28.3495, 3)
    })

    it('should convert temperature units correctly', () => {
      // Celsius to Fahrenheit
      const result1 = convertUnit(0, 'celsius', 'fahrenheit')
      expect(result1.result).toBeCloseTo(32, 1)

      const result2 = convertUnit(100, 'celsius', 'fahrenheit')
      expect(result2.result).toBeCloseTo(212, 1)

      // Fahrenheit to Celsius
      const result3 = convertUnit(32, 'fahrenheit', 'celsius')
      expect(result3.result).toBeCloseTo(0, 1)

      const result4 = convertUnit(212, 'fahrenheit', 'celsius')
      expect(result4.result).toBeCloseTo(100, 1)
    })

    it('should convert data storage units correctly', () => {
      // Bytes to Kilobytes (using decimal conversion)
      const result1 = convertUnit(1000, 'B', 'KB')
      expect(result1.result).toBeCloseTo(1, 1)

      // Megabytes to Gigabytes
      const result2 = convertUnit(1000, 'MB', 'GB')
      expect(result2.result).toBeCloseTo(1, 1)

      // Gigabytes to Terabytes
      const result3 = convertUnit(1000, 'GB', 'TB')
      expect(result3.result).toBeCloseTo(1, 1)
    })

    it('should convert time units correctly', () => {
      // Hours to minutes
      const result1 = convertUnit(1, 'h', 'min')
      expect(result1.result).toBe(60)

      // Days to hours
      const result2 = convertUnit(1, 'd', 'h')
      expect(result2.result).toBe(24)

      // Seconds to milliseconds
      const result3 = convertUnit(1, 's', 'ms')
      expect(result3.result).toBe(1000)
    })

    it('should handle decimal values correctly', () => {
      const result = convertUnit(2.5, 'ft', 'm')
      expect(result.result).toBeCloseTo(0.762, 3)
      expect(result.value).toBe(2.5)
    })

    it('should handle zero values', () => {
      const result = convertUnit(0, 'ft', 'm')
      expect(result.result).toBe(0)
    })

    it('should handle negative values', () => {
      const result = convertUnit(-10, 'celsius', 'fahrenheit')
      expect(result.result).toBeCloseTo(14, 1)
    })

    it('should throw error for invalid unit conversion', () => {
      expect(() => convertUnit(1, 'invalid', 'm')).toThrow()
      expect(() => convertUnit(1, 'ft', 'invalid')).toThrow()
    })

    it('should throw error for incompatible unit types', () => {
      // Can't convert length to mass
      expect(() => convertUnit(1, 'ft', 'kg')).toThrow()
      // Can't convert temperature to length
      expect(() => convertUnit(1, 'celsius', 'm')).toThrow()
    })
  })

  describe('formatResult', () => {
    it('should format large numbers correctly', () => {
      const result1 = formatResult(1000000, 'm')
      expect(result1).toContain('1000000')
      expect(result1).toContain('m')

      const result2 = formatResult(1234567, 'ft')
      expect(result2).toContain('1200000') // Our formatter uses toPrecision, not scientific notation
    })

    it('should format small numbers correctly', () => {
      const result1 = formatResult(0.001, 'mm')
      expect(result1).toContain('0.001')
      expect(result1).toContain('mm')

      const result2 = formatResult(0.000001, 'g')
      expect(result2).toContain('0.000001') // Our formatter uses toPrecision, not scientific notation
    })

    it('should format medium numbers correctly', () => {
      const result = formatResult(123.456, 'kg')
      expect(result).toContain('123.5')
      expect(result).toContain('kg')
    })

    it('should remove trailing zeros', () => {
      const result = formatResult(1.000, 'm')
      expect(result).toBe('1 m')
    })
  })

  describe('getUnitSymbol', () => {
    it('should return correct symbols for known units', () => {
      expect(getUnitSymbol('m')).toBe('m')
      expect(getUnitSymbol('ft')).toBe('ft')
      expect(getUnitSymbol('kg')).toBe('kg')
      expect(getUnitSymbol('celsius')).toBe('°C')
      expect(getUnitSymbol('fahrenheit')).toBe('°F')
    })

    it('should return unit ID for unknown units', () => {
      expect(getUnitSymbol('unknown')).toBe('unknown')
    })
  })

  describe('getUnitName', () => {
    it('should return correct names for known units', () => {
      expect(getUnitName('m')).toBe('Meter')
      expect(getUnitName('ft')).toBe('Foot')
      expect(getUnitName('kg')).toBe('Kilogram')
      expect(getUnitName('celsius')).toBe('Celsius')
    })

    it('should return unit ID for unknown units', () => {
      expect(getUnitName('unknown')).toBe('unknown')
    })
  })

  describe('getCategoryByUnit', () => {
    it('should return correct category for known units', () => {
      const lengthCategory = getCategoryByUnit('m')
      expect(lengthCategory?.id).toBe('length')
      expect(lengthCategory?.name).toBe('Length')

      const massCategory = getCategoryByUnit('kg')
      expect(massCategory?.id).toBe('mass')
      expect(massCategory?.name).toBe('Weight & Mass')

      const tempCategory = getCategoryByUnit('celsius')
      expect(tempCategory?.id).toBe('temperature')
    })

    it('should return undefined for unknown units', () => {
      expect(getCategoryByUnit('unknown')).toBeUndefined()
    })
  })

  describe('getPopularConversions', () => {
    it('should return popular conversions for length', () => {
      const conversions = getPopularConversions('length')
      expect(conversions).toHaveLength(4)
      expect(conversions[0]).toEqual({
        from: 'ft',
        to: 'm',
        label: 'Feet to Meters'
      })
    })

    it('should return popular conversions for mass', () => {
      const conversions = getPopularConversions('mass')
      expect(conversions).toHaveLength(4)
      expect(conversions[0]).toEqual({
        from: 'lb',
        to: 'kg',
        label: 'Pounds to Kilograms'
      })
    })

    it('should return empty array for unknown category', () => {
      const conversions = getPopularConversions('unknown')
      expect(conversions).toEqual([])
    })
  })

  describe('unitCategories', () => {
    it('should have all expected categories', () => {
      const categoryIds = unitCategories.map(c => c.id)
      expect(categoryIds).toContain('length')
      expect(categoryIds).toContain('mass')
      expect(categoryIds).toContain('temperature')
      expect(categoryIds).toContain('area')
      expect(categoryIds).toContain('time')
      expect(categoryIds).toContain('data')
      expect(categoryIds).toContain('energy')
      expect(categoryIds).toContain('power')
    })

    it('should have valid structure for each category', () => {
      unitCategories.forEach(category => {
        expect(category).toHaveProperty('id')
        expect(category).toHaveProperty('name')
        expect(category).toHaveProperty('description')
        expect(category).toHaveProperty('icon')
        expect(category).toHaveProperty('units')
        expect(Array.isArray(category.units)).toBe(true)
        expect(category.units.length).toBeGreaterThan(0)

        category.units.forEach(unit => {
          expect(unit).toHaveProperty('id')
          expect(unit).toHaveProperty('name')
          expect(unit).toHaveProperty('symbol')
        })
      })
    })
  })
})
