# Sitemap Generation Script

This script automatically generates a comprehensive XML sitemap for Convert.land including all conversion pages across multiple languages.

## Usage

### Generate Sitemap Manually
```bash
npm run sitemap
```

### Auto-generate Before Build
The sitemap is automatically generated before each build via the `prebuild` script.

## Generated Files

### `public/sitemap.xml`
- Complete XML sitemap following Google's sitemap protocol
- No `lastmod` timestamps (as requested)
- 600+ URLs across 10 languages
- Organized by priority levels

### `public/robots.txt`
- Search engine friendly robots.txt
- References the sitemap location
- Includes crawl delays for different bots
- Blocks AI training bots (optional)

## URL Structure & Priorities

### Priority 1.0 - Homepage
- `/` (English)
- `/{lang}/` (All languages)

### Priority 0.9 - Category Overview
- `/convert` (All categories page)
- `/{lang}/convert` (All languages)

### Priority 0.8 - Category Pages
- `/convert/{category}` (e.g., `/convert/length`)
- `/{lang}/convert/{category}` (All languages)

### Priority 0.7 - Popular Conversions
- `/convert/{category}/{from}-to-{to}` (e.g., `/convert/length/feet-to-meters`)
- `/{lang}/convert/{category}/{from}-to-{to}` (All languages)

### Priority 0.6 - SEO-Friendly Routes
- `/{conversion}` (e.g., `/meters-to-feet`)
- `/{lang}/{conversion}` (All languages)

## Languages Supported

- English (en) - default
- Chinese Simplified (zh-hans)
- Spanish (es)
- French (fr)
- German (de)
- Japanese (ja)
- Korean (ko)
- Portuguese (pt)
- Russian (ru)
- Arabic (ar)

## Categories Included

1. **Length** (📏) - meters, feet, inches, kilometers, etc.
2. **Weight & Mass** (⚖️) - kilograms, pounds, grams, etc.
3. **Temperature** (🌡️) - Celsius, Fahrenheit, Kelvin
4. **Area** (📐) - square meters, square feet, acres, etc.
5. **Volume** (🥤) - liters, gallons, milliliters, etc.
6. **Time** (⏰) - seconds, minutes, hours, days, etc.
7. **Data Storage** (💾) - bytes, kilobytes, megabytes, etc.
8. **Energy** (⚡) - joules, kilojoules, calories, etc.
9. **Power** (🔌) - watts, kilowatts, horsepower, etc.

## Technical Details

- **Format**: XML Sitemap Protocol 0.9
- **Encoding**: UTF-8
- **Total URLs**: ~600 (60 per language)
- **File Size**: ~25KB
- **Update Frequency**: On build/deploy

## SEO Benefits

- ✅ Complete search engine discoverability
- ✅ Multi-language hreflang support
- ✅ Organized priority structure
- ✅ Popular conversion pages included
- ✅ Category and landing pages covered
- ✅ Robots.txt with proper sitemap reference

## Maintenance

The script automatically reads from:
- `utils/converter.js` - Category and unit definitions
- Built-in popular conversions mapping
- Predefined SEO-friendly route list

When adding new categories or units, simply update the converter utilities and regenerate the sitemap.