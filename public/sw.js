// Service Worker for Convert.land PWA
const CACHE_NAME = 'convert-land-v1'
const STATIC_CACHE_NAME = 'convert-land-static-v1'

// URLs to cache
const STATIC_URLS = [
  '/',
  '/manifest.json',
  '/favicon.ico'
]

// Install event - cache static resources
self.addEventListener('install', event => {
  console.log('Service Worker installing')
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then(cache => {
        console.log('Caching static resources')
        return cache.addAll(STATIC_URLS)
      })
      .then(() => {
        return self.skipWaiting()
      })
  )
})

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  console.log('Service Worker activating')
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== CACHE_NAME && cacheName !== STATIC_CACHE_NAME) {
            console.log('Deleting old cache:', cacheName)
            return caches.delete(cacheName)
          }
        })
      )
    }).then(() => {
      return self.clients.claim()
    })
  )
})

// Fetch event - serve from cache with network fallback
self.addEventListener('fetch', event => {
  const { request } = event
  const url = new URL(request.url)

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return
  }

  // Skip external requests
  if (url.origin !== location.origin) {
    return
  }

  // Cache strategy based on request type
  if (request.destination === 'image') {
    // Images: Cache first, network fallback
    event.respondWith(
      caches.match(request).then(cached => {
        if (cached) {
          return cached
        }
        return fetch(request).then(response => {
          if (response.ok) {
            const responseClone = response.clone()
            caches.open(CACHE_NAME).then(cache => {
              cache.put(request, responseClone)
            })
          }
          return response
        })
      })
    )
  } else if (url.pathname.startsWith('/api/')) {
    // API: Network first, cache fallback
    event.respondWith(
      fetch(request).then(response => {
        if (response.ok) {
          const responseClone = response.clone()
          caches.open(CACHE_NAME).then(cache => {
            cache.put(request, responseClone)
          })
        }
        return response
      }).catch(() => {
        return caches.match(request)
      })
    )
  } else {
    // Pages: Stale while revalidate
    event.respondWith(
      caches.match(request).then(cached => {
        const fetchPromise = fetch(request).then(response => {
          if (response.ok) {
            const responseClone = response.clone()
            caches.open(CACHE_NAME).then(cache => {
              cache.put(request, responseClone)
            })
          }
          return response
        })

        return cached || fetchPromise
      })
    )
  }
})

// Background sync for offline conversion history
self.addEventListener('sync', event => {
  if (event.tag === 'conversion-sync') {
    event.waitUntil(syncConversions())
  }
})

// Push notifications (future feature)
self.addEventListener('push', event => {
  if (event.data) {
    const data = event.data.json()
    const options = {
      body: data.body,
      icon: '/icons/icon-192x192.png',
      badge: '/icons/icon-72x72.png',
      tag: 'convert-land-notification',
      renotify: true
    }
    
    event.waitUntil(
      self.registration.showNotification(data.title, options)
    )
  }
})

// Notification click handler
self.addEventListener('notificationclick', event => {
  event.notification.close()
  
  event.waitUntil(
    clients.openWindow('/')
  )
})

// Helper function to sync conversions (placeholder)
async function syncConversions() {
  // Implementation for syncing offline conversions
  console.log('Syncing conversions...')
}