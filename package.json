{"name": "convert-land", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "test": "vitest", "test:ui": "vitest --ui", "sitemap": "node scripts/generate-sitemap.js", "prebuild": "npm run sitemap"}, "dependencies": {"@nuxtjs/i18n": "^9.5.4", "@tailwindcss/vite": "^4.1.7", "convert": "^5.1.0", "nuxt": "^3.17.4", "tailwindcss": "^4.1.7", "vue": "^3.5.15", "vue-router": "^4.5.1"}, "devDependencies": {"@vitest/ui": "^2.1.8", "jsdom": "^26.1.0", "vitest": "^2.1.8"}, "packageManager": "pnpm@9.5.0+sha512.140036830124618d624a2187b50d04289d5a087f326c9edfc0ccd733d76c4f52c3a313d4fc148794a2a9d81553016004e6742e8cf850670268a7387fc220c903"}