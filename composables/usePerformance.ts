// Performance optimization composables

export const useLazyLoad = () => {
  const observeElement = (el: HTMLElement, callback: () => void) => {
    if (process.client && 'IntersectionObserver' in window) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            callback()
            observer.unobserve(entry.target)
          }
        })
      }, {
        rootMargin: '50px'
      })
      
      observer.observe(el)
      
      return () => observer.disconnect()
    }
    
    // Fallback: execute immediately if IntersectionObserver is not supported
    callback()
    return () => {}
  }
  
  return { observeElement }
}

/**
 * Preload critical resources
 */
export const usePreload = () => {
  const preloadCSS = (href: string) => {
    if (process.client) {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'style'
      link.href = href
      document.head.appendChild(link)
    }
  }
  
  const preloadJS = (src: string) => {
    if (process.client) {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'script'
      link.href = src
      document.head.appendChild(link)
    }
  }
  
  const prefetchRoute = (route: string) => {
    if (process.client) {
      const link = document.createElement('link')
      link.rel = 'prefetch'
      link.href = route
      document.head.appendChild(link)
    }
  }
  
  return { preloadCSS, preloadJS, prefetchRoute }
}

/**
 * Performance monitoring
 */
export const usePerformanceMonitoring = () => {
  const measureTiming = (name: string, fn: () => void | Promise<void>) => {
    if (process.client && 'performance' in window) {
      const start = performance.now()
      const result = fn()
      
      if (result instanceof Promise) {
        return result.then(() => {
          const end = performance.now()
          console.log(`${name} took ${end - start} milliseconds`)
        })
      } else {
        const end = performance.now()
        console.log(`${name} took ${end - start} milliseconds`)
      }
    }
    
    return fn()
  }
  
  const getWebVitals = () => {
    if (process.client && 'performance' in window) {
      return {
        navigationStart: performance.timeOrigin,
        domContentLoaded: performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming,
      }
    }
    return null
  }
  
  return { measureTiming, getWebVitals }
}

/**
 * Debounce function for performance optimization
 */
export const useDebounce = () => {
  const debounce = <T extends (...args: any[]) => void>(
    func: T,
    wait: number,
    immediate = false
  ): T => {
    let timeout: NodeJS.Timeout | null = null
    
    return ((...args: Parameters<T>) => {
      const later = () => {
        timeout = null
        if (!immediate) func(...args)
      }
      
      const callNow = immediate && !timeout
      
      if (timeout) clearTimeout(timeout)
      timeout = setTimeout(later, wait)
      
      if (callNow) func(...args)
    }) as T
  }
  
  const throttle = <T extends (...args: any[]) => void>(
    func: T,
    limit: number
  ): T => {
    let inThrottle: boolean
    
    return ((...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args)
        inThrottle = true
        setTimeout(() => (inThrottle = false), limit)
      }
    }) as T
  }
  
  return { debounce, throttle }
}