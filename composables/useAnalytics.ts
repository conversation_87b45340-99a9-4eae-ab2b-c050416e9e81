/**
 * Analytics integration composable
 */
export const useAnalytics = () => {
  const config = useRuntimeConfig()
  const { isOnline } = usePWA()

  // Initialize analytics
  const initAnalytics = () => {
    if (!process.client) return

    // Initialize Google Analytics 4
    initGA4()
    
    // Initialize error monitoring
    initErrorMonitoring()
    
    // Initialize performance monitoring
    initPerformanceMonitoring()
  }

  // Google Analytics 4
  const initGA4 = () => {
    if (!process.client) return

    // Load gtag script
    const script = document.createElement('script')
    script.async = true
    script.src = 'https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX'
    document.head.appendChild(script)

    // Initialize gtag
    window.dataLayer = window.dataLayer || []
    function gtag(...args: any[]) {
      window.dataLayer.push(arguments)
    }
    window.gtag = gtag

    gtag('js', new Date())
    gtag('config', 'G-XXXXXXXXXX', {
      page_title: document.title,
      page_location: window.location.href
    })
  }

  // Track page views
  const trackPageView = (path: string, title?: string) => {
    if (!process.client || !window.gtag) return

    window.gtag('config', 'G-XXXXXXXXXX', {
      page_path: path,
      page_title: title || document.title
    })
  }

  // Track conversion events
  const trackConversion = (conversionData: {
    category: string
    fromUnit: string
    toUnit: string
    value: number
    result: number
  }) => {
    if (!process.client) return

    // Google Analytics event
    if (window.gtag) {
      window.gtag('event', 'unit_conversion', {
        event_category: 'conversions',
        event_label: `${conversionData.fromUnit}_to_${conversionData.toUnit}`,
        custom_parameter_1: conversionData.category,
        value: conversionData.value
      })
    }

    // Custom analytics event
    trackCustomEvent('conversion', {
      category: conversionData.category,
      conversion_type: `${conversionData.fromUnit}_to_${conversionData.toUnit}`,
      input_value: conversionData.value,
      output_value: conversionData.result,
      timestamp: new Date().toISOString()
    })
  }

  // Track user interactions
  const trackInteraction = (action: string, element: string, value?: string | number) => {
    if (!process.client) return

    if (window.gtag) {
      window.gtag('event', action, {
        event_category: 'engagement',
        event_label: element,
        value: value
      })
    }
  }

  // Track performance metrics
  const trackPerformance = (metrics: {
    metric_name: string
    value: number
    unit: string
  }) => {
    if (!process.client || !window.gtag) return

    window.gtag('event', 'performance_metric', {
      event_category: 'performance',
      metric_name: metrics.metric_name,
      metric_value: metrics.value,
      metric_unit: metrics.unit
    })
  }

  // Track errors
  const trackError = (error: {
    message: string
    source?: string
    stack?: string
    user_agent?: string
  }) => {
    if (!process.client) return

    if (window.gtag) {
      window.gtag('event', 'exception', {
        description: error.message,
        fatal: false,
        custom_parameter_source: error.source,
        custom_parameter_stack: error.stack?.substring(0, 500) // Limit stack trace
      })
    }

    // Also log to console in development
    if (import.meta.env.DEV) {
      console.error('Analytics Error:', error)
    }
  }

  // Track search queries
  const trackSearch = (query: string, results_count?: number) => {
    if (!process.client || !window.gtag) return

    window.gtag('event', 'search', {
      search_term: query,
      custom_parameter_results: results_count
    })
  }

  // Track PWA events
  const trackPWAEvent = (event_type: 'install_prompt' | 'install_accept' | 'install_dismiss') => {
    if (!process.client || !window.gtag) return

    window.gtag('event', 'pwa_interaction', {
      event_category: 'pwa',
      event_label: event_type
    })
  }

  // Initialize error monitoring (simplified)
  const initErrorMonitoring = () => {
    if (!process.client) return

    window.addEventListener('error', (event) => {
      trackError({
        message: event.message,
        source: event.filename,
        stack: event.error?.stack,
        user_agent: navigator.userAgent
      })
    })

    window.addEventListener('unhandledrejection', (event) => {
      trackError({
        message: `Unhandled Promise Rejection: ${event.reason}`,
        source: 'promise',
        user_agent: navigator.userAgent
      })
    })
  }

  // Initialize performance monitoring
  const initPerformanceMonitoring = () => {
    if (!process.client || !('PerformanceObserver' in window)) return

    // Core Web Vitals
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.name === 'FCP') {
          trackPerformance({
            metric_name: 'first_contentful_paint',
            value: entry.startTime,
            unit: 'milliseconds'
          })
        } else if (entry.name === 'LCP') {
          trackPerformance({
            metric_name: 'largest_contentful_paint',
            value: entry.startTime,
            unit: 'milliseconds'
          })
        } else if (entry.name === 'FID') {
          trackPerformance({
            metric_name: 'first_input_delay',
            value: entry.processingStart - entry.startTime,
            unit: 'milliseconds'
          })
        }
      }
    })

    try {
      observer.observe({ entryTypes: ['paint', 'largest-contentful-paint', 'first-input'] })
    } catch (e) {
      // Some browsers might not support all entry types
      console.warn('Performance observer failed:', e)
    }
  }

  // Custom event tracking (for future backend integration)
  const trackCustomEvent = async (event_type: string, data: any) => {
    if (!process.client || !isOnline.value) return

    try {
      // Store locally if offline
      const events = JSON.parse(localStorage.getItem('analytics_events') || '[]')
      events.push({
        type: event_type,
        data,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        user_agent: navigator.userAgent
      })
      
      // Keep only last 100 events
      if (events.length > 100) {
        events.splice(0, events.length - 100)
      }
      
      localStorage.setItem('analytics_events', JSON.stringify(events))

      // TODO: Send to backend when implemented
      // await $fetch('/api/analytics', {
      //   method: 'POST',
      //   body: { event_type, data }
      // })
    } catch (error) {
      console.warn('Failed to track custom event:', error)
    }
  }

  // Get analytics data (for dashboard)
  const getAnalyticsData = () => {
    if (!process.client) return []

    try {
      return JSON.parse(localStorage.getItem('analytics_events') || '[]')
    } catch {
      return []
    }
  }

  // A/B testing helper
  const getVariant = (testName: string, variants: string[]) => {
    if (!process.client) return variants[0]

    // Simple hash-based variant selection
    const userId = localStorage.getItem('user_id') || 'anonymous'
    const hash = simpleHash(testName + userId)
    const index = hash % variants.length
    
    // Track variant assignment
    trackCustomEvent('ab_test_assignment', {
      test_name: testName,
      variant: variants[index],
      user_id: userId
    })
    
    return variants[index]
  }

  // Simple hash function for A/B testing
  const simpleHash = (str: string) => {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32bit integer
    }
    return Math.abs(hash)
  }

  // GDPR compliance helper
  const requestAnalyticsConsent = () => {
    if (!process.client) return false

    const consent = localStorage.getItem('analytics_consent')
    if (consent === 'granted') return true
    if (consent === 'denied') return false

    // Show consent banner (simplified)
    const granted = confirm('This site uses analytics to improve user experience. Do you consent?')
    localStorage.setItem('analytics_consent', granted ? 'granted' : 'denied')
    
    if (granted) {
      initAnalytics()
    }
    
    return granted
  }

  return {
    initAnalytics,
    trackPageView,
    trackConversion,
    trackInteraction,
    trackPerformance,
    trackError,
    trackSearch,
    trackPWAEvent,
    trackCustomEvent,
    getAnalyticsData,
    getVariant,
    requestAnalyticsConsent
  }
}

// Global type definitions
declare global {
  interface Window {
    dataLayer: any[]
    gtag: (...args: any[]) => void
  }
}