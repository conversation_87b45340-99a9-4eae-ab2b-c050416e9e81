/**
 * PWA functionality composable
 */
export const usePWA = () => {
  const isInstallable = ref(false)
  const isInstalled = ref(false)
  const isOnline = ref(true)
  const swRegistration = ref<ServiceWorkerRegistration | null>(null)
  let deferredPrompt: any = null

  // Register Service Worker
  const registerServiceWorker = async () => {
    if (!process.client || !('serviceWorker' in navigator)) {
      console.log('Service Worker not supported')
      return
    }

    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      })
      
      swRegistration.value = registration
      console.log('Service Worker registered successfully')

      // Listen for updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // New content available, show update notification
              showUpdateNotification()
            }
          })
        }
      })

    } catch (error) {
      console.error('Service Worker registration failed:', error)
    }
  }

  // Check if app is installable
  const checkInstallability = () => {
    if (!process.client) return

    // Listen for beforeinstallprompt event
    window.addEventListener('beforeinstallprompt', (e) => {
      e.preventDefault()
      deferredPrompt = e
      isInstallable.value = true
    })

    // Check if already installed
    window.addEventListener('appinstalled', () => {
      isInstalled.value = true
      isInstallable.value = false
      deferredPrompt = null
    })

    // Check for iOS standalone mode
    if (window.navigator.standalone || window.matchMedia('(display-mode: standalone)').matches) {
      isInstalled.value = true
    }
  }

  // Install the PWA
  const installPWA = async () => {
    if (!deferredPrompt) return false

    try {
      deferredPrompt.prompt()
      const { outcome } = await deferredPrompt.userChoice
      
      if (outcome === 'accepted') {
        isInstalled.value = true
        isInstallable.value = false
      }
      
      deferredPrompt = null
      return outcome === 'accepted'
    } catch (error) {
      console.error('PWA installation failed:', error)
      return false
    }
  }

  // Monitor online/offline status
  const monitorNetworkStatus = () => {
    if (!process.client) return

    const updateOnlineStatus = () => {
      isOnline.value = navigator.onLine
    }

    window.addEventListener('online', updateOnlineStatus)
    window.addEventListener('offline', updateOnlineStatus)
    
    // Initial check
    updateOnlineStatus()
  }

  // Show update notification
  const showUpdateNotification = () => {
    // This could trigger a toast notification
    console.log('New version available! Please refresh.')
  }

  // Update the service worker
  const updateServiceWorker = async () => {
    if (!swRegistration.value) return

    try {
      await swRegistration.value.update()
      window.location.reload()
    } catch (error) {
      console.error('Service Worker update failed:', error)
    }
  }

  // Cache conversion for offline use
  const cacheConversion = async (conversion: any) => {
    if (!process.client || !('caches' in window)) return

    try {
      const cache = await caches.open('convert-land-conversions')
      const key = `conversion-${Date.now()}`
      const response = new Response(JSON.stringify(conversion), {
        headers: { 'Content-Type': 'application/json' }
      })
      await cache.put(key, response)
    } catch (error) {
      console.error('Failed to cache conversion:', error)
    }
  }

  // Get cached conversions
  const getCachedConversions = async () => {
    if (!process.client || !('caches' in window)) return []

    try {
      const cache = await caches.open('convert-land-conversions')
      const keys = await cache.keys()
      const conversions = []

      for (const key of keys) {
        const response = await cache.match(key)
        if (response) {
          const data = await response.json()
          conversions.push(data)
        }
      }

      return conversions
    } catch (error) {
      console.error('Failed to get cached conversions:', error)
      return []
    }
  }

  // Get PWA capabilities
  const getCapabilities = () => {
    if (!process.client) return {}

    return {
      serviceWorker: 'serviceWorker' in navigator,
      notifications: 'Notification' in window,
      backgroundSync: 'serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype,
      pushManager: 'serviceWorker' in navigator && 'PushManager' in window,
      share: 'share' in navigator,
      webShare: 'canShare' in navigator
    }
  }

  // Initialize PWA
  const initPWA = () => {
    if (!process.client) return

    registerServiceWorker()
    checkInstallability()
    monitorNetworkStatus()
  }

  // Share functionality
  const shareConversion = async (conversion: any) => {
    if (!process.client) return false

    const shareData = {
      title: 'Convert.land - Unit Conversion',
      text: `${conversion.value} ${conversion.fromUnit} = ${conversion.formatted}`,
      url: window.location.href
    }

    try {
      if (navigator.share && navigator.canShare && navigator.canShare(shareData)) {
        await navigator.share(shareData)
        return true
      } else {
        // Fallback to clipboard
        await navigator.clipboard.writeText(`${shareData.text} - ${shareData.url}`)
        return true
      }
    } catch (error) {
      console.error('Share failed:', error)
      return false
    }
  }

  return {
    isInstallable: readonly(isInstallable),
    isInstalled: readonly(isInstalled),
    isOnline: readonly(isOnline),
    installPWA,
    initPWA,
    updateServiceWorker,
    cacheConversion,
    getCachedConversions,
    getCapabilities,
    shareConversion
  }
}