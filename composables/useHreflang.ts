/**
 * Generate hreflang tags for multilingual SEO
 */
export const useHreflang = () => {
  const { locale } = useI18n()
  const route = useRoute()
  const baseUrl = 'https://convert.land'
  
  // Available locales
  const availableLocales = [
    'en', 'zh-hans', 'es', 'fr', 'de', 'ja', 'ko', 'pt', 'ru', 'ar'
  ]
  
  const generateHreflangLinks = (currentPath?: string) => {
    const path = currentPath || route.path
    const cleanPath = path === '/' ? '' : path
    
    const links: Array<{ rel: string; hreflang: string; href: string }> = []
    
    // Add hreflang for each locale
    availableLocales.forEach(localeCode => {
      let href = baseUrl
      
      if (localeCode === 'en') {
        // English is default, no prefix needed
        href += cleanPath
      } else {
        // Other languages have prefix
        href += `/${localeCode}${cleanPath}`
      }
      
      links.push({
        rel: 'alternate',
        hreflang: localeCode,
        href
      })
    })
    
    // Add x-default (fallback to English)
    links.push({
      rel: 'alternate',
      hreflang: 'x-default',
      href: baseUrl + cleanPath
    })
    
    return links
  }
  
  // Generate localized URLs for each category
  const generateCategoryHreflang = (categoryId: string) => {
    return generateHreflangLinks(`/convert/${categoryId}`)
  }
  
  // Generate localized URLs for specific conversions
  const generateConversionHreflang = (categoryId: string, conversionParam: string) => {
    return generateHreflangLinks(`/convert/${categoryId}/${conversionParam}`)
  }
  
  // Generate localized URLs for SEO routes
  const generateSEORouteHreflang = (seoPath: string) => {
    return generateHreflangLinks(seoPath)
  }
  
  // Get current locale information
  const getCurrentLocaleInfo = () => {
    const localeNames: Record<string, string> = {
      'en': 'English',
      'zh-hans': '简体中文',
      'es': 'Español',
      'fr': 'Français',
      'de': 'Deutsch',
      'ja': '日本語',
      'ko': '한국어',
      'pt': 'Português',
      'ru': 'Русский',
      'ar': 'العربية'
    }
    
    return {
      code: locale.value,
      name: localeNames[locale.value] || 'English',
      isRTL: locale.value === 'ar'
    }
  }
  
  // Get region-specific unit preferences
  const getRegionalUnitPreferences = () => {
    const preferences: Record<string, {
      lengthUnits: string[]
      weightUnits: string[]
      temperatureUnit: string
    }> = {
      'en': {
        lengthUnits: ['ft', 'in', 'mi', 'yd'],
        weightUnits: ['lb', 'oz'],
        temperatureUnit: 'fahrenheit'
      },
      'zh-hans': {
        lengthUnits: ['m', 'cm', 'km', 'mm'],
        weightUnits: ['kg', 'g'],
        temperatureUnit: 'celsius'
      },
      'es': {
        lengthUnits: ['m', 'cm', 'km'],
        weightUnits: ['kg', 'g'],
        temperatureUnit: 'celsius'
      },
      'fr': {
        lengthUnits: ['m', 'cm', 'km'],
        weightUnits: ['kg', 'g'],
        temperatureUnit: 'celsius'
      },
      'de': {
        lengthUnits: ['m', 'cm', 'km'],
        weightUnits: ['kg', 'g'],
        temperatureUnit: 'celsius'
      },
      'ja': {
        lengthUnits: ['m', 'cm', 'km'],
        weightUnits: ['kg', 'g'],
        temperatureUnit: 'celsius'
      },
      'ko': {
        lengthUnits: ['m', 'cm', 'km'],
        weightUnits: ['kg', 'g'],
        temperatureUnit: 'celsius'
      },
      'pt': {
        lengthUnits: ['m', 'cm', 'km'],
        weightUnits: ['kg', 'g'],
        temperatureUnit: 'celsius'
      },
      'ru': {
        lengthUnits: ['m', 'cm', 'km'],
        weightUnits: ['kg', 'g'],
        temperatureUnit: 'celsius'
      },
      'ar': {
        lengthUnits: ['m', 'cm', 'km'],
        weightUnits: ['kg', 'g'],
        temperatureUnit: 'celsius'
      }
    }
    
    return preferences[locale.value] || preferences['en']
  }
  
  return {
    generateHreflangLinks,
    generateCategoryHreflang,
    generateConversionHreflang,
    generateSEORouteHreflang,
    getCurrentLocaleInfo,
    getRegionalUnitPreferences
  }
}