import { unitCategories, getPopularConversions } from '~/utils/converter'
import { getSitemapUrls } from '~/utils/seo-routes'

export default defineEventHandler(async (event) => {
  const baseUrl = 'https://convert.land'
  
  // Generate all URLs
  const urls: Array<{ loc: string; lastmod: string; changefreq: string; priority: string }> = []
  
  // Homepage
  urls.push({
    loc: baseUrl,
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'daily',
    priority: '1.0'
  })
  
  // Add SEO-optimized specific conversion routes
  const seoUrls = getSitemapUrls(baseUrl)
  seoUrls.forEach(seoUrl => {
    urls.push({
      loc: seoUrl.url,
      lastmod: seoUrl.lastmod,
      changefreq: seoUrl.changefreq,
      priority: seoUrl.priority.toString()
    })
  })
  
  // Category pages
  unitCategories.forEach(category => {
    urls.push({
      loc: `${baseUrl}/convert/${category.id}`,
      lastmod: new Date().toISOString().split('T')[0],
      changefreq: 'weekly',
      priority: '0.8'
    })
    
    // Popular conversion pages for each category
    const popularConversions = getPopularConversions(category.id)
    popularConversions.forEach(conversion => {
      urls.push({
        loc: `${baseUrl}/convert/${category.id}/${conversion.from}-to-${conversion.to}`,
        lastmod: new Date().toISOString().split('T')[0],
        changefreq: 'monthly',
        priority: '0.6'
      })
    })
    
    // All possible conversion combinations (limited to avoid too many URLs)
    const units = category.units.slice(0, 6) // Limit to first 6 units to avoid explosion
    units.forEach(fromUnit => {
      units.forEach(toUnit => {
        if (fromUnit.id !== toUnit.id) {
          // Only add if not already in popular conversions
          const isPopular = popularConversions.some(
            conv => conv.from === fromUnit.id && conv.to === toUnit.id
          )
          if (!isPopular) {
            urls.push({
              loc: `${baseUrl}/convert/${category.id}/${fromUnit.id}-to-${toUnit.id}`,
              lastmod: new Date().toISOString().split('T')[0],
              changefreq: 'monthly',
              priority: '0.4'
            })
          }
        }
      })
    })
  })
  
  // Generate XML
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urls.map(url => `  <url>
    <loc>${url.loc}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>
  </url>`).join('\n')}
</urlset>`

  setHeader(event, 'Content-Type', 'application/xml')
  return sitemap
})
