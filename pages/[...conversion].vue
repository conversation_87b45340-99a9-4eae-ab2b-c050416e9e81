<template>
  <div>
    <!-- <PERSON> Header -->
    <div class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <Breadcrumb :items="breadcrumbItems" />
        <div class="mt-4">
          <h1 class="text-3xl font-bold text-gray-900">
            {{ conversionData.title }}
          </h1>
          <p class="text-gray-600 mt-1">
            {{ conversionData.description }}
          </p>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Converter Interface -->
      <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <SpecificConverter 
          v-if="conversionData.categoryId && conversionData.fromUnit && conversionData.toUnit"
          :category="category"
          :from-unit="conversionData.fromUnit"
          :to-unit="conversionData.toUnit" />
        <QuickConverter v-else />
      </div>

      <!-- SEO Content -->
      <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">
          About {{ conversionData.title }}
        </h2>
        <div class="prose prose-blue max-w-none">
          <p class="text-gray-700 leading-relaxed">
            {{ conversionData.content }}
          </p>
          
          <div v-if="conversionData.formula" class="mt-6 bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Conversion Formula</h3>
            <p class="font-mono text-gray-800">{{ conversionData.formula }}</p>
          </div>
          
          <div v-if="conversionData.examples" class="mt-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Common Conversions</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div 
                v-for="example in conversionData.examples" 
                :key="example.from"
                class="bg-gray-50 p-3 rounded">
                <span class="font-medium">{{ example.from }}</span> = 
                <span class="text-blue-600 font-semibold">{{ example.to }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Related Conversions -->
      <div class="bg-white rounded-lg shadow-lg p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">
          Related Conversions
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <NuxtLink
            v-for="related in relatedConversions"
            :key="related.path"
            :to="related.path"
            class="block p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all duration-200">
            <div class="font-medium text-gray-900">{{ related.title }}</div>
            <div class="text-sm text-gray-600 mt-1">{{ related.description }}</div>
          </NuxtLink>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { unitCategories, getPopularConversions } from '~/utils/converter'
import { generateSEORoutes, getRouteMeta } from '~/utils/seo-routes'

const route = useRoute()
const conversionPath = route.params.conversion as string[]
const fullPath = '/' + conversionPath.join('/')

// Define conversion mappings
const conversionMappings: Record<string, {
  title: string
  description: string
  content: string
  formula?: string
  examples?: Array<{ from: string; to: string }>
  categoryId?: string
  fromUnit?: string
  toUnit?: string
}> = {
  'meters-to-feet': {
    title: 'Meters to Feet Converter',
    description: 'Convert meters to feet instantly with our free online converter',
    content: 'The meter to feet conversion is one of the most commonly used length conversions. The meter is the base unit of length in the International System of Units (SI), while the foot is a unit of length in the imperial and US customary measurement systems.',
    formula: 'feet = meters × 3.28084',
    examples: [
      { from: '1 meter', to: '3.28084 feet' },
      { from: '5 meters', to: '16.4042 feet' },
      { from: '10 meters', to: '32.8084 feet' },
      { from: '100 meters', to: '328.084 feet' }
    ],
    categoryId: 'length',
    fromUnit: 'm',
    toUnit: 'ft'
  },
  'feet-to-meters': {
    title: 'Feet to Meters Converter',
    description: 'Convert feet to meters instantly with our free online converter',
    content: 'The foot to meter conversion is essential for international communication and scientific calculations. One foot is defined as exactly 0.3048 meters.',
    formula: 'meters = feet × 0.3048',
    examples: [
      { from: '1 foot', to: '0.3048 meters' },
      { from: '5 feet', to: '1.524 meters' },
      { from: '10 feet', to: '3.048 meters' },
      { from: '100 feet', to: '30.48 meters' }
    ],
    categoryId: 'length',
    fromUnit: 'ft',
    toUnit: 'm'
  },
  'kg-to-pounds': {
    title: 'Kilograms to Pounds Converter',
    description: 'Convert kilograms to pounds instantly with our free online converter',
    content: 'The kilogram to pound conversion is widely used in fitness, cooking, and international trade. The kilogram is the base unit of mass in the SI system, while the pound is used in the imperial system.',
    formula: 'pounds = kilograms × 2.20462',
    examples: [
      { from: '1 kg', to: '2.20462 lbs' },
      { from: '5 kg', to: '11.0231 lbs' },
      { from: '10 kg', to: '22.0462 lbs' },
      { from: '70 kg', to: '154.324 lbs' }
    ],
    categoryId: 'mass',
    fromUnit: 'kg',
    toUnit: 'lb'
  }
  // Add more conversions as needed
}

// Get conversion data
const conversionData = computed(() => {
  const data = conversionMappings[fullPath.slice(1)] // Remove leading slash
  
  if (!data) {
    // Fallback for unknown conversions
    return {
      title: 'Unit Converter',
      description: 'Free online unit converter for all types of measurements',
      content: 'Convert between different units of measurement quickly and accurately.'
    }
  }
  
  return data
})

// Get category if available
const category = computed(() => {
  if (!conversionData.value.categoryId) return null
  return unitCategories.find(c => c.id === conversionData.value.categoryId)
})

// Get related conversions
const relatedConversions = computed(() => {
  const routes = generateSEORoutes()
  return routes
    .filter(r => r.path !== fullPath)
    .slice(0, 6)
    .map(r => ({
      path: r.path,
      title: r.title,
      description: r.description
    }))
})

// Breadcrumb items
const breadcrumbItems = computed(() => {
  const items = [{ name: 'Home', path: '/' }]
  
  if (conversionData.value.categoryId) {
    items.push({
      name: `${category.value?.name || 'Unit'} Converter`,
      path: `/convert/${conversionData.value.categoryId}`
    })
  }
  
  items.push({
    name: conversionData.value.title,
    path: fullPath
  })
  
  return items
})

// SEO Meta
const metaData = getRouteMeta(fullPath)
const { generateSEORouteHreflang } = useHreflang()

if (metaData) {
  useHead({
    ...metaData,
    link: [
      ...(metaData.link || []),
      ...generateSEORouteHreflang(fullPath)
    ]
  })
} else {
  useHead({
    title: conversionData.value.title,
    meta: [
      { name: 'description', content: conversionData.value.description }
    ],
    link: generateSEORouteHreflang(fullPath)
  })
}

// 404 for truly unknown paths
if (!conversionMappings[fullPath.slice(1)] && !conversionData.value) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Conversion not found'
  })
}
</script>