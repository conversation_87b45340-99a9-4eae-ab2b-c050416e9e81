<template>
  <div class="bg-gray-50 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-8">SEO Test Page</h1>
      
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">Test URLs with Values</h2>
        <div class="space-y-4">
          <div>
            <h3 class="font-medium text-gray-900">Length Conversions</h3>
            <ul class="mt-2 space-y-2 text-sm">
              <li>
                <a href="/convert/length/feet-to-meters?value=5" class="text-blue-600 hover:underline">
                  5 feet to meters
                </a>
                <span class="text-gray-500 ml-2">→ Should show "5 ft to m = 1.524 m" in title</span>
              </li>
              <li>
                <a href="/convert/length/inches-to-centimeters?value=12" class="text-blue-600 hover:underline">
                  12 inches to centimeters
                </a>
                <span class="text-gray-500 ml-2">→ Should show "12 in to cm = 30.48 cm" in title</span>
              </li>
              <li>
                <a href="/convert/length/kilometers-to-miles?value=10" class="text-blue-600 hover:underline">
                  10 kilometers to miles
                </a>
                <span class="text-gray-500 ml-2">→ Should show "10 km to mi = 6.21371 mi" in title</span>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 class="font-medium text-gray-900">Temperature Conversions</h3>
            <ul class="mt-2 space-y-2 text-sm">
              <li>
                <a href="/convert/temperature/celsius-to-fahrenheit?value=100" class="text-blue-600 hover:underline">
                  100 celsius to fahrenheit
                </a>
                <span class="text-gray-500 ml-2">→ Should show "100 °C to °F = 212 °F" in title</span>
              </li>
              <li>
                <a href="/convert/temperature/fahrenheit-to-celsius?value=32" class="text-blue-600 hover:underline">
                  32 fahrenheit to celsius
                </a>
                <span class="text-gray-500 ml-2">→ Should show "32 °F to °C = 0 °C" in title</span>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 class="font-medium text-gray-900">Weight Conversions</h3>
            <ul class="mt-2 space-y-2 text-sm">
              <li>
                <a href="/convert/mass/pounds-to-kilograms?value=100" class="text-blue-600 hover:underline">
                  100 pounds to kilograms
                </a>
                <span class="text-gray-500 ml-2">→ Should show "100 lb to kg = 45.3592 kg" in title</span>
              </li>
              <li>
                <a href="/convert/mass/kilograms-to-pounds?value=50" class="text-blue-600 hover:underline">
                  50 kilograms to pounds
                </a>
                <span class="text-gray-500 ml-2">→ Should show "50 kg to lb = 110.231 lb" in title</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">Category Pages with Query Parameters</h2>
        <div class="space-y-4">
          <div>
            <h3 class="font-medium text-gray-900">Length Category</h3>
            <ul class="mt-2 space-y-2 text-sm">
              <li>
                <a href="/convert/length?from=ft&to=m&value=5" class="text-blue-600 hover:underline">
                  Length converter with 5 ft to m
                </a>
                <span class="text-gray-500 ml-2">→ Should show conversion result in title</span>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 class="font-medium text-gray-900">Temperature Category</h3>
            <ul class="mt-2 space-y-2 text-sm">
              <li>
                <a href="/convert/temperature?from=celsius&to=fahrenheit&value=25" class="text-blue-600 hover:underline">
                  Temperature converter with 25°C to °F
                </a>
                <span class="text-gray-500 ml-2">→ Should show conversion result in title</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4">SEO Features Implemented</h2>
        <ul class="space-y-2 text-sm text-gray-700">
          <li>✅ Dynamic titles with conversion results when URL contains value parameter</li>
          <li>✅ Dynamic descriptions with actual conversion results</li>
          <li>✅ Server-side rendered Quick Reference tables for SEO</li>
          <li>✅ Comprehensive conversion tables (30 values instead of 13)</li>
          <li>✅ Structured data (JSON-LD) for search engines</li>
          <li>✅ Individual pages for each conversion combination</li>
          <li>✅ URL parameters preserved and used for SEO optimization</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
useHead({
  title: 'SEO Test Page - Convert.land',
  meta: [
    { name: 'description', content: 'Test page for SEO features of Convert.land unit converter' }
  ]
})
</script>
