<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- Header -->
    <header class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">Convert.land</h1>
            <p class="text-gray-600 mt-1">{{ $t('description') }}</p>
          </div>
          <div class="flex items-center space-x-4">
            <NuxtLink
              :to="switchLocalePath('zh')"
              class="text-sm text-gray-600 hover:text-gray-900">
              中文
            </NuxtLink>
            <NuxtLink
              :to="switchLocalePath('en')"
              class="text-sm text-gray-600 hover:text-gray-900">
              English
            </NuxtLink>
          </div>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div class="text-center">
        <h2 class="text-4xl font-bold text-gray-900 sm:text-5xl">
          {{ $t('converter.convertAnyUnit') }}
        </h2>
        <p class="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
          {{ $t('converter.fastAccurateEasy') }}
        </p>
      </div>
    </section>

    <!-- Quick Converter -->
    <section class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 mb-16">
      <div class="bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">{{ $t('converter.quickConvert') }}</h3>
        <QuickConverter />
      </div>
    </section>

    <!-- Categories Grid -->
    <section class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
      <h3 class="text-2xl font-bold text-gray-900 mb-8 text-center">
        {{ $t('converter.chooseCategory') }}
      </h3>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <NuxtLink
          v-for="category in unitCategories"
          :key="category.id"
          :to="`/convert/${category.id}`"
          class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 p-6 group">
          <div class="text-center">
            <div class="text-4xl mb-3">{{ category.icon }}</div>
            <h4 class="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
              {{ $t(`categories.${category.id}`) }}
            </h4>
            <p class="text-sm text-gray-600 mt-2">
              {{ category.description }}
            </p>
            <div class="mt-4 text-xs text-gray-500">
              {{ category.units.length }} {{ $t('converter.unitsAvailable') }}
            </div>
          </div>
        </NuxtLink>
      </div>
    </section>

    <!-- Popular Conversions -->
    <section class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
      <h3 class="text-2xl font-bold text-gray-900 mb-8 text-center">
        {{ $t('converter.popularConversions') }}
      </h3>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <NuxtLink
          v-for="conversion in popularConversions"
          :key="`${conversion.from}-${conversion.to}`"
          :to="`/convert/${conversion.category}?from=${conversion.from}&to=${conversion.to}`"
          class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 p-4 border border-gray-200">
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-gray-900">{{ conversion.label }}</span>
            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </div>
        </NuxtLink>
      </div>
    </section>

    <!-- Features -->
    <section class="bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <h3 class="text-2xl font-bold text-gray-900 mb-12 text-center">
          {{ $t('converter.whyChoose') }}
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div class="text-center">
            <div class="text-3xl mb-4">⚡</div>
            <h4 class="text-lg font-semibold text-gray-900 mb-2">{{ $t('converter.lightningFast') }}</h4>
            <p class="text-gray-600">{{ $t('converter.lightningFastDesc') }}</p>
          </div>
          <div class="text-center">
            <div class="text-3xl mb-4">🎯</div>
            <h4 class="text-lg font-semibold text-gray-900 mb-2">{{ $t('converter.highlyAccurate') }}</h4>
            <p class="text-gray-600">{{ $t('converter.highlyAccurateDesc') }}</p>
          </div>
          <div class="text-center">
            <div class="text-3xl mb-4">📱</div>
            <h4 class="text-lg font-semibold text-gray-900 mb-2">{{ $t('converter.mobileFriendly') }}</h4>
            <p class="text-gray-600">{{ $t('converter.mobileFriendlyDesc') }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div class="col-span-1 md:col-span-2">
            <h4 class="text-lg font-semibold mb-4">Convert.land</h4>
            <p class="text-gray-400 mb-4">
              The most comprehensive and user-friendly unit converter on the web.
              Convert between hundreds of units across multiple categories.
            </p>
          </div>
          <div>
            <h5 class="font-semibold mb-4">Categories</h5>
            <ul class="space-y-2 text-sm text-gray-400">
              <li v-for="category in unitCategories.slice(0, 4)" :key="category.id">
                <NuxtLink :to="`/convert/${category.id}`" class="hover:text-white">
                  {{ $t(`categories.${category.id}`) }}
                </NuxtLink>
              </li>
            </ul>
          </div>
          <div>
            <h5 class="font-semibold mb-4">More Categories</h5>
            <ul class="space-y-2 text-sm text-gray-400">
              <li v-for="category in unitCategories.slice(4)" :key="category.id">
                <NuxtLink :to="`/convert/${category.id}`" class="hover:text-white">
                  {{ $t(`categories.${category.id}`) }}
                </NuxtLink>
              </li>
            </ul>
          </div>
        </div>
        <div class="border-t border-gray-800 mt-8 pt-8 text-center text-sm text-gray-400">
          <p>&copy; 2024 Convert.land. All rights reserved.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { unitCategories, getPopularConversions } from '~/utils/converter'

// SEO with i18n
const { t } = useI18n()
useHead(() => ({
  title: t('title'),
  meta: [
    { name: 'description', content: t('description') }
  ]
}))

const switchLocalePath = useSwitchLocalePath()

// Get popular conversions from all categories
const popularConversions = computed(() => {
  const allConversions: Array<{from: string, to: string, label: string, category: string}> = []

  unitCategories.forEach(category => {
    const conversions = getPopularConversions(category.id)
    conversions.forEach(conversion => {
      allConversions.push({
        ...conversion,
        category: category.id
      })
    })
  })

  return allConversions.slice(0, 12) // Show top 12 popular conversions
})
</script>
