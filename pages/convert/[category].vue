<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- Header -->
    <header class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <nav class="flex items-center space-x-4 text-sm">
          <NuxtLink to="/" class="text-blue-600 hover:text-blue-800">{{ $t('home') }}</NuxtLink>
          <span class="text-gray-400">/</span>
          <span class="text-gray-600">{{ category?.name || 'Convert' }}</span>
        </nav>
        <div class="mt-4">
          <h1 class="text-3xl font-bold text-gray-900 flex items-center">
            <span class="text-4xl mr-3">{{ category?.icon }}</span>
            {{ category ? $t(`categories.${category.id}`) : 'Unit' }} {{ $t('converter.quickConvert') }}
          </h1>
          <p class="text-gray-600 mt-1">{{ category?.description }}</p>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Converter Interface -->
      <div v-if="category" class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <CategoryConverter :category="category" />
      </div>

      <!-- Popular Conversions -->
      <div v-if="popularConversions.length > 0" class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">
          {{ $t('converter.popularConversions') }} {{ category ? $t(`categories.${category.id}`) : '' }}
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <NuxtLink
            v-for="conversion in popularConversions"
            :key="`${conversion.from}-${conversion.to}`"
            :to="`/convert/${categoryId}/${conversion.from}-to-${conversion.to}`"
            class="block p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all duration-200">
            <div class="font-medium text-gray-900">{{ conversion.label }}</div>
            <div class="text-sm text-gray-600 mt-1">
              {{ getUnitName(conversion.from) }} → {{ getUnitName(conversion.to) }}
            </div>
          </NuxtLink>
        </div>
      </div>

      <!-- All Units Table -->
      <div v-if="category" class="bg-white rounded-lg shadow-lg p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">
          {{ $t(`categories.${category.id}`) }} {{ $t('converter.unitsAvailable') }}
        </h2>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Unit
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Symbol
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="unit in category.units" :key="unit.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ unit.name }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ unit.symbol }}
                </td>
                <td class="px-6 py-4 text-sm text-gray-500">
                  {{ unit.description || `Unit of ${category.name.toLowerCase()}` }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </main>

    <!-- Related Categories -->
    <section class="bg-white border-t">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <h2 class="text-2xl font-bold text-gray-900 mb-8 text-center">
          {{ $t('converter.otherCategories') }}
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <NuxtLink
            v-for="otherCategory in otherCategories"
            :key="otherCategory.id"
            :to="`/convert/${otherCategory.id}`"
            class="bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 p-4 group">
            <div class="text-center">
              <div class="text-3xl mb-2">{{ otherCategory.icon }}</div>
              <h4 class="text-sm font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                {{ $t(`categories.${otherCategory.id}`) }}
              </h4>
            </div>
          </NuxtLink>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { unitCategories, getPopularConversions, getUnitName, convertUnit } from '~/utils/converter'

// Get route parameter
const route = useRoute()
const categoryId = computed(() => route.params.category as string)

// Find the category
const category = computed(() => {
  const cat = unitCategories.find(c => c.id === categoryId.value)
  if (!cat && process.client) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Category not found'
    })
  }
  return cat
})

// Get popular conversions for this category
const popularConversions = computed(() =>
  categoryId.value ? getPopularConversions(categoryId.value) : []
)

// Get other categories (excluding current one)
const otherCategories = computed(() =>
  unitCategories.filter(c => c.id !== categoryId.value)
)

// Get URL parameters for SEO
const urlFrom = computed(() => route.query.from as string)
const urlTo = computed(() => route.query.to as string)
const urlValue = computed(() => route.query.value as string)

// Calculate conversion result for SEO if all parameters are provided
const seoConversionResult = computed(() => {
  if (!urlFrom.value || !urlTo.value || !urlValue.value || !category.value) return null

  try {
    const value = parseFloat(urlValue.value)
    if (isNaN(value)) return null

    const fromUnit = category.value.units.find(u => u.id === urlFrom.value)
    const toUnit = category.value.units.find(u => u.id === urlTo.value)

    if (!fromUnit || !toUnit) return null

    const result = convertUnit(value, urlFrom.value, urlTo.value)
    return {
      ...result,
      fromUnit: fromUnit,
      toUnit: toUnit
    }
  } catch {
    return null
  }
})

// Dynamic SEO title and description
const seoTitle = computed(() => {
  if (seoConversionResult.value && category.value) {
    return `${urlValue.value} ${seoConversionResult.value.fromUnit.symbol} to ${seoConversionResult.value.toUnit.symbol} = ${seoConversionResult.value.formatted} - Convert.land`
  }
  return category.value ? `${category.value.name} Converter - Convert.land` : 'Convert.land'
})

const seoDescription = computed(() => {
  if (seoConversionResult.value && category.value) {
    return `${urlValue.value} ${seoConversionResult.value.fromUnit.name} equals ${seoConversionResult.value.formatted}. Convert ${category.value.name.toLowerCase()} units easily with our free online converter.`
  }
  return category.value ? `Convert ${category.value.name.toLowerCase()} units easily. ${category.value.description}. Free online ${category.value.name.toLowerCase()} converter with ${category.value.units.length} units.` : ''
})

// SEO
useHead(() => ({
  title: seoTitle.value,
  meta: category.value ? [
    {
      name: 'description',
      content: seoDescription.value
    },
    {
      name: 'keywords',
      content: `${category.value.name.toLowerCase()} converter, ${category.value.units.map(u => u.name.toLowerCase()).join(', ')}, unit conversion`
    }
  ] : [],
  script: category.value ? [
    {
      type: 'application/ld+json',
      children: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'WebApplication',
        name: `${category.value.name} Converter`,
        description: category.value.description,
        url: `https://convert.land/convert/${categoryId.value}`,
        applicationCategory: 'UtilityApplication',
        operatingSystem: 'Any',
        offers: {
          '@type': 'Offer',
          price: '0',
          priceCurrency: 'USD'
        }
      })
    }
  ] : []
}))
</script>
