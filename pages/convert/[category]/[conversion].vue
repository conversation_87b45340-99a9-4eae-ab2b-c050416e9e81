<template>
  <div>
    <!-- <PERSON>er -->
    <div class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <Breadcrumb :items="[
          { name: $t('home'), path: '/' },
          { name: $t(`categories.${categoryId}`) + ' ' + $t('converter.converter'), path: `/convert/${categoryId}` },
          { name: conversionTitle.value, path: `/convert/${categoryId}/${conversionParam}` }
        ]" />
        <div class="mt-4">
          <h1 class="text-3xl font-bold text-gray-900">
            {{ conversionTitle.value }}
          </h1>
          <p class="text-gray-600 mt-1">
            {{ $t('converter.convertInstantly', { from: $t(`units.${fromUnitId}`), to: $t(`units.${toUnitId}`) }) }}
          </p>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Converter Interface -->
      <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <SpecificConverter 
          :category="category"
          :from-unit="fromUnitId"
          :to-unit="toUnitId" />
      </div>

      <!-- Conversion Information -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Conversion Formula -->
        <div class="bg-white rounded-lg shadow-lg p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">
            {{ $t('converter.howToConvert', { from: $t(`units.${fromUnitId}`), to: $t(`units.${toUnitId}`) }) }}
          </h2>
          <div class="space-y-4">
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="text-sm text-gray-600 mb-2">{{ $t('converter.formula') }}</div>
              <div class="font-mono text-lg">
                {{ $t(`units.${toUnitId}`) }} = {{ $t(`units.${fromUnitId}`) }} × {{ conversionFactor }}
              </div>
            </div>
            <div class="text-sm text-gray-600">
              <p>{{ $t('converter.toConvertFrom', {
                from: $t(`units.${fromUnitId}`),
                to: $t(`units.${toUnitId}`),
                fromLower: $t(`units.${fromUnitId}`).toLowerCase(),
                factor: conversionFactor
              }) }}</p>
            </div>
          </div>
        </div>

        <!-- Quick Examples -->
        <div class="bg-white rounded-lg shadow-lg p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">
            {{ $t('converter.commonConversions', { from: $t(`units.${fromUnitId}`), to: $t(`units.${toUnitId}`) }) }}
          </h2>
          <div class="space-y-3">
            <div 
              v-for="example in examples" 
              :key="example.from"
              class="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
              <span class="text-gray-700">{{ example.from }} {{ fromUnitSymbol }}</span>
              <span class="font-semibold text-gray-900">{{ example.to }} {{ toUnitSymbol }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Reference for SEO -->
      <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">
          {{ $t('converter.quickReference', { from: $t(`units.${fromUnitId}`), to: $t(`units.${toUnitId}`) }) }}
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div
            v-for="ref in quickReferenceValues"
            :key="ref.from"
            class="bg-gray-50 rounded-lg p-4 text-center">
            <div class="text-lg font-semibold text-gray-900">{{ ref.fromFormatted }}</div>
            <div class="text-sm text-gray-500 my-1">=</div>
            <div class="text-lg font-bold text-blue-600">{{ ref.toFormatted }}</div>
          </div>
        </div>
      </div>

      <!-- Conversion Table -->
      <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">
          {{ $t('converter.conversionTable', { from: $t(`units.${fromUnitId}`), to: $t(`units.${toUnitId}`) }) }}
        </h2>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t(`units.${fromUnitId}`) }} ({{ fromUnitSymbol }})
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t(`units.${toUnitId}`) }} ({{ toUnitSymbol }})
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="row in conversionTable" :key="row.from" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ row.from }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ row.to }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Related Conversions -->
      <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">
          {{ $t('converter.relatedConversions', { category: $t(`categories.${categoryId}`) }) }}
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <NuxtLink
            v-for="related in relatedConversions"
            :key="`${related.from}-${related.to}`"
            :to="`/convert/${categoryId}/${related.from}-to-${related.to}`"
            class="block p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all duration-200">
            <div class="font-medium text-gray-900">{{ related.label }}</div>
            <div class="text-sm text-gray-600 mt-1">
              {{ getUnitName(related.from) }} → {{ getUnitName(related.to) }}
            </div>
          </NuxtLink>
        </div>
      </div>

      <!-- Cross-Category Links -->
      <div class="bg-white rounded-lg shadow-lg p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">
          Explore Other Conversion Types
        </h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <NuxtLink
            v-for="otherCategory in otherCategories.slice(0, 4)"
            :key="otherCategory.id"
            :to="`/convert/${otherCategory.id}`"
            class="text-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors group">
            <div class="text-2xl mb-2">{{ otherCategory.icon }}</div>
            <div class="text-sm font-medium text-gray-900 group-hover:text-blue-600">
              {{ $t(`categories.${otherCategory.id}`) }}
            </div>
          </NuxtLink>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { unitCategories, convertUnit, getUnitName, getUnitSymbol, getPopularConversions } from '~/utils/converter'

const { t } = useI18n()
const { generateConversionHreflang } = useHreflang()

// Get route parameters
const route = useRoute()
const categoryId = route.params.category
const conversionParam = route.params.conversion

// Parse conversion parameter (e.g., "feet-to-meters")
const [fromUnitId, , toUnitId] = conversionParam.split('-')

// Find the category
const category = unitCategories.find(c => c.id === categoryId)

// Validate category and units
if (!category) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Category not found'
  })
}

const fromUnit = category.units.find(u => u.id === fromUnitId)
const toUnit = category.units.find(u => u.id === toUnitId)

if (!fromUnit || !toUnit) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Conversion not found'
  })
}

// Computed properties
const fromUnitName = computed(() => t(`units.${fromUnitId}`))
const toUnitName = computed(() => t(`units.${toUnitId}`))
const fromUnitSymbol = fromUnit.symbol
const toUnitSymbol = toUnit.symbol
const conversionTitle = computed(() => `${fromUnitName.value} ${t('converter.to')} ${toUnitName.value} ${t('converter.converter')}`)

// Calculate conversion factor
const conversionFactor = computed(() => {
  try {
    const result = convertUnit(1, fromUnitId, toUnitId)
    return result.result.toPrecision(6)
  } catch {
    return '1'
  }
})

// Generate examples
const examples = computed(() => {
  const values = [1, 5, 10, 25, 50, 100]
  return values.map(value => {
    try {
      const result = convertUnit(value, fromUnitId, toUnitId)
      return {
        from: value.toString(),
        to: parseFloat(result.result.toPrecision(4)).toString()
      }
    } catch {
      return { from: value.toString(), to: '0' }
    }
  })
})

// Generate comprehensive conversion table for SEO (server-side rendered)
const conversionTable = (() => {
  const values = [0.1, 0.25, 0.5, 0.75, 1, 1.5, 2, 2.5, 3, 4, 5, 6, 7, 8, 9, 10, 15, 20, 25, 30, 40, 50, 75, 100, 150, 200, 250, 500, 750, 1000]
  return values.map(value => {
    try {
      const result = convertUnit(value, fromUnitId, toUnitId)
      return {
        from: value.toString(),
        to: parseFloat(result.result.toPrecision(6)).toString(),
        formatted: result.formatted
      }
    } catch {
      return { from: value.toString(), to: '0', formatted: '0' }
    }
  })
})()

// Generate quick reference values for common conversions (server-side rendered)
const quickReferenceValues = (() => {
  const commonValues = [1, 5, 10, 25, 50, 100]
  return commonValues.map(value => {
    try {
      const result = convertUnit(value, fromUnitId, toUnitId)
      return {
        from: value,
        to: result.result,
        fromFormatted: `${value} ${fromUnitSymbol}`,
        toFormatted: result.formatted
      }
    } catch {
      return {
        from: value,
        to: 0,
        fromFormatted: `${value} ${fromUnitSymbol}`,
        toFormatted: `0 ${toUnitSymbol}`
      }
    }
  })
})()

// Get related conversions
const relatedConversions = computed(() => {
  const popular = getPopularConversions(categoryId)
  // Filter out current conversion and limit to 6
  return popular
    .filter(conv => !(conv.from === fromUnitId && conv.to === toUnitId))
    .slice(0, 6)
})

// Get other categories for cross-category links
const otherCategories = computed(() => {
  return unitCategories.filter(c => c.id !== categoryId)
})

// Get URL value for SEO (route already defined above)
const urlValue = computed(() => route.query.value as string)

// Calculate result for SEO if value is provided
const seoResult = computed(() => {
  if (!urlValue.value) return null
  try {
    const value = parseFloat(urlValue.value)
    if (isNaN(value)) return null
    return convertUnit(value, fromUnitId, toUnitId)
  } catch {
    return null
  }
})

// Dynamic title with conversion result for SEO
const seoTitle = computed(() => {
  if (seoResult.value) {
    return `${urlValue.value} ${fromUnitSymbol} to ${toUnitSymbol} = ${seoResult.value.formatted} - Convert.land`
  }
  return `${conversionTitle.value} - Convert.land`
})

// Dynamic description with conversion result for SEO
const seoDescription = computed(() => {
  if (seoResult.value) {
    return `${urlValue.value} ${fromUnitName.value} equals ${seoResult.value.formatted}. Convert ${fromUnitName.value.toLowerCase()} to ${toUnitName.value.toLowerCase()} instantly with our free online converter.`
  }
  return `Convert ${fromUnitName.value} to ${toUnitName.value} instantly. Free online ${fromUnitName.value.toLowerCase()} to ${toUnitName.value.toLowerCase()} converter with conversion table and formula.`
})

// Enhanced SEO keywords
const seoKeywords = computed(() => {
  const keywords = [
    `${fromUnitName.value.toLowerCase()} to ${toUnitName.value.toLowerCase()}`,
    `${fromUnitId} to ${toUnitId}`,
    `convert ${fromUnitName.value.toLowerCase()} to ${toUnitName.value.toLowerCase()}`,
    `${fromUnitSymbol} to ${toUnitSymbol}`,
    `${fromUnitName.value.toLowerCase()} ${toUnitName.value.toLowerCase()} converter`,
    `${t(`categories.${categoryId}`).toLowerCase()} converter`,
    `${t(`categories.${categoryId}`).toLowerCase()} conversion`,
    'unit converter',
    'conversion calculator',
    'online converter',
    'free converter'
  ]
  return keywords.join(', ')
})

// SEO
useHead(() => ({
  title: seoTitle.value,
  meta: [
    {
      name: 'description',
      content: seoDescription.value
    },
    {
      name: 'keywords',
      content: seoKeywords.value
    },
    {
      name: 'robots',
      content: 'index, follow'
    },
    {
      property: 'og:title',
      content: seoTitle.value
    },
    {
      property: 'og:description',
      content: seoDescription.value
    },
    {
      property: 'og:type',
      content: 'website'
    },
    {
      property: 'og:url',
      content: `https://convert.land/convert/${categoryId}/${conversionParam}`
    },
    {
      property: 'og:image',
      content: `https://convert.land/images/${fromUnitId}-to-${toUnitId}-converter.png`
    },
    {
      name: 'twitter:card',
      content: 'summary_large_image'
    },
    {
      name: 'twitter:title',
      content: seoTitle.value
    },
    {
      name: 'twitter:description',
      content: seoDescription.value
    },
    {
      name: 'twitter:image',
      content: `https://convert.land/images/${fromUnitId}-to-${toUnitId}-converter.png`
    }
  ],
  link: [
    {
      rel: 'canonical',
      href: `https://convert.land/convert/${categoryId}/${conversionParam}`
    },
    ...generateConversionHreflang(categoryId, conversionParam)
  ]
}))

// Structured data for SEO
useHead({
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'WebApplication',
        name: conversionTitle.value,
        description: `Convert ${fromUnitName.value} to ${toUnitName.value} instantly`,
        url: `https://convert.land/convert/${categoryId}/${conversionParam}`,
        applicationCategory: 'UtilityApplication',
        operatingSystem: 'Any',
        offers: {
          '@type': 'Offer',
          price: '0',
          priceCurrency: 'USD'
        },
        screenshot: `https://convert.land/screenshots/${categoryId}-${fromUnitId}-to-${toUnitId}.png`
      })
    },
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'HowTo',
        name: `How to Convert ${fromUnitName.value} to ${toUnitName.value}`,
        description: `Step-by-step guide to convert ${fromUnitName.value.toLowerCase()} to ${toUnitName.value.toLowerCase()}`,
        image: `https://convert.land/images/${fromUnitId}-to-${toUnitId}-guide.jpg`,
        step: [
          {
            '@type': 'HowToStep',
            name: 'Enter Value',
            text: `Enter the ${fromUnitName.value.toLowerCase()} value you want to convert`,
            image: `https://convert.land/images/enter-${fromUnitId}-value.jpg`
          },
          {
            '@type': 'HowToStep',
            name: 'Apply Formula',
            text: `Multiply by ${conversionFactor.value} to get ${toUnitName.value.toLowerCase()}`,
            image: `https://convert.land/images/apply-conversion-formula.jpg`
          },
          {
            '@type': 'HowToStep',
            name: 'Get Result',
            text: `The result is displayed in ${toUnitName.value.toLowerCase()}`,
            image: `https://convert.land/images/conversion-result.jpg`
          }
        ],
        totalTime: 'PT15S',
        tool: {
          '@type': 'HowToTool',
          name: 'Convert.land Unit Converter'
        }
      })
    },
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'BreadcrumbList',
        itemListElement: [
          {
            '@type': 'ListItem',
            position: 1,
            name: 'Home',
            item: 'https://convert.land/'
          },
          {
            '@type': 'ListItem',
            position: 2,
            name: `${t(`categories.${categoryId}`)} Converter`,
            item: `https://convert.land/convert/${categoryId}`
          },
          {
            '@type': 'ListItem',
            position: 3,
            name: conversionTitle.value,
            item: `https://convert.land/convert/${categoryId}/${conversionParam}`
          }
        ]
      })
    },
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'FAQPage',
        mainEntity: [
          {
            '@type': 'Question',
            name: `How to convert ${fromUnitName.value} to ${toUnitName.value}?`,
            acceptedAnswer: {
              '@type': 'Answer',
              text: `To convert ${fromUnitName.value} to ${toUnitName.value}, multiply the ${fromUnitName.value.toLowerCase()} value by ${conversionFactor.value}. Formula: ${toUnitName.value} = ${fromUnitName.value} × ${conversionFactor.value}`
            }
          },
          {
            '@type': 'Question',
            name: `What is the conversion factor from ${fromUnitName.value} to ${toUnitName.value}?`,
            acceptedAnswer: {
              '@type': 'Answer',
              text: `The conversion factor from ${fromUnitName.value} to ${toUnitName.value} is ${conversionFactor.value}. This means 1 ${fromUnitSymbol} = ${conversionFactor.value} ${toUnitSymbol}.`
            }
          },
          {
            '@type': 'Question',
            name: `How many ${toUnitName.value} in 1 ${fromUnitName.value}?`,
            acceptedAnswer: {
              '@type': 'Answer',
              text: `There are ${conversionFactor.value} ${toUnitName.value.toLowerCase()} in 1 ${fromUnitName.value.toLowerCase()}.`
            }
          }
        ]
      })
    }
  ]
})
</script>
