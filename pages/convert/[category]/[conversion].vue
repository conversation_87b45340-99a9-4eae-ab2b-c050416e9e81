<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- Header -->
    <header class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <nav class="flex items-center space-x-4 text-sm">
          <NuxtLink to="/" class="text-blue-600 hover:text-blue-800">{{ $t('home') }}</NuxtLink>
          <span class="text-gray-400">/</span>
          <NuxtLink :to="`/convert/${categoryId}`" class="text-blue-600 hover:text-blue-800">
            {{ $t(`categories.${categoryId}`) }}
          </NuxtLink>
          <span class="text-gray-400">/</span>
          <span class="text-gray-600">{{ conversionTitle.value }}</span>
        </nav>
        <div class="mt-4">
          <h1 class="text-3xl font-bold text-gray-900">
            {{ conversionTitle.value }}
          </h1>
          <p class="text-gray-600 mt-1">
            {{ $t('converter.convertInstantly', { from: $t(`units.${fromUnitId}`), to: $t(`units.${toUnitId}`) }) }}
          </p>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Converter Interface -->
      <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <SpecificConverter 
          :category="category"
          :from-unit="fromUnitId"
          :to-unit="toUnitId" />
      </div>

      <!-- Conversion Information -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Conversion Formula -->
        <div class="bg-white rounded-lg shadow-lg p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">
            {{ $t('converter.howToConvert', { from: $t(`units.${fromUnitId}`), to: $t(`units.${toUnitId}`) }) }}
          </h2>
          <div class="space-y-4">
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="text-sm text-gray-600 mb-2">{{ $t('converter.formula') }}</div>
              <div class="font-mono text-lg">
                {{ $t(`units.${toUnitId}`) }} = {{ $t(`units.${fromUnitId}`) }} × {{ conversionFactor }}
              </div>
            </div>
            <div class="text-sm text-gray-600">
              <p>{{ $t('converter.toConvertFrom', {
                from: $t(`units.${fromUnitId}`),
                to: $t(`units.${toUnitId}`),
                fromLower: $t(`units.${fromUnitId}`).toLowerCase(),
                factor: conversionFactor
              }) }}</p>
            </div>
          </div>
        </div>

        <!-- Quick Examples -->
        <div class="bg-white rounded-lg shadow-lg p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">
            {{ $t('converter.commonConversions', { from: $t(`units.${fromUnitId}`), to: $t(`units.${toUnitId}`) }) }}
          </h2>
          <div class="space-y-3">
            <div 
              v-for="example in examples" 
              :key="example.from"
              class="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
              <span class="text-gray-700">{{ example.from }} {{ fromUnitSymbol }}</span>
              <span class="font-semibold text-gray-900">{{ example.to }} {{ toUnitSymbol }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Reference for SEO -->
      <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">
          {{ $t('converter.quickReference', { from: $t(`units.${fromUnitId}`), to: $t(`units.${toUnitId}`) }) }}
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div
            v-for="ref in quickReferenceValues"
            :key="ref.from"
            class="bg-gray-50 rounded-lg p-4 text-center">
            <div class="text-lg font-semibold text-gray-900">{{ ref.fromFormatted }}</div>
            <div class="text-sm text-gray-500 my-1">=</div>
            <div class="text-lg font-bold text-blue-600">{{ ref.toFormatted }}</div>
          </div>
        </div>
      </div>

      <!-- Conversion Table -->
      <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">
          {{ $t('converter.conversionTable', { from: $t(`units.${fromUnitId}`), to: $t(`units.${toUnitId}`) }) }}
        </h2>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t(`units.${fromUnitId}`) }} ({{ fromUnitSymbol }})
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t(`units.${toUnitId}`) }} ({{ toUnitSymbol }})
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="row in conversionTable" :key="row.from" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ row.from }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ row.to }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Related Conversions -->
      <div class="bg-white rounded-lg shadow-lg p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">
          {{ $t('converter.relatedConversions', { category: $t(`categories.${categoryId}`) }) }}
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <NuxtLink
            v-for="related in relatedConversions"
            :key="`${related.from}-${related.to}`"
            :to="`/convert/${categoryId}/${related.from}-to-${related.to}`"
            class="block p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all duration-200">
            <div class="font-medium text-gray-900">{{ related.label }}</div>
            <div class="text-sm text-gray-600 mt-1">
              {{ getUnitName(related.from) }} → {{ getUnitName(related.to) }}
            </div>
          </NuxtLink>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { unitCategories, convertUnit, getUnitName, getUnitSymbol, getPopularConversions } from '~/utils/converter'

const { t } = useI18n()

// Get route parameters
const route = useRoute()
const categoryId = route.params.category
const conversionParam = route.params.conversion

// Parse conversion parameter (e.g., "feet-to-meters")
const [fromUnitId, , toUnitId] = conversionParam.split('-')

// Find the category
const category = unitCategories.find(c => c.id === categoryId)

// Validate category and units
if (!category) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Category not found'
  })
}

const fromUnit = category.units.find(u => u.id === fromUnitId)
const toUnit = category.units.find(u => u.id === toUnitId)

if (!fromUnit || !toUnit) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Conversion not found'
  })
}

// Computed properties
const fromUnitName = computed(() => t(`units.${fromUnitId}`))
const toUnitName = computed(() => t(`units.${toUnitId}`))
const fromUnitSymbol = fromUnit.symbol
const toUnitSymbol = toUnit.symbol
const conversionTitle = computed(() => `${fromUnitName.value} ${t('converter.to')} ${toUnitName.value} ${t('converter.converter')}`)

// Calculate conversion factor
const conversionFactor = computed(() => {
  try {
    const result = convertUnit(1, fromUnitId, toUnitId)
    return result.result.toPrecision(6)
  } catch {
    return '1'
  }
})

// Generate examples
const examples = computed(() => {
  const values = [1, 5, 10, 25, 50, 100]
  return values.map(value => {
    try {
      const result = convertUnit(value, fromUnitId, toUnitId)
      return {
        from: value.toString(),
        to: parseFloat(result.result.toPrecision(4)).toString()
      }
    } catch {
      return { from: value.toString(), to: '0' }
    }
  })
})

// Generate comprehensive conversion table for SEO (server-side rendered)
const conversionTable = (() => {
  const values = [0.1, 0.25, 0.5, 0.75, 1, 1.5, 2, 2.5, 3, 4, 5, 6, 7, 8, 9, 10, 15, 20, 25, 30, 40, 50, 75, 100, 150, 200, 250, 500, 750, 1000]
  return values.map(value => {
    try {
      const result = convertUnit(value, fromUnitId, toUnitId)
      return {
        from: value.toString(),
        to: parseFloat(result.result.toPrecision(6)).toString(),
        formatted: result.formatted
      }
    } catch {
      return { from: value.toString(), to: '0', formatted: '0' }
    }
  })
})()

// Generate quick reference values for common conversions (server-side rendered)
const quickReferenceValues = (() => {
  const commonValues = [1, 5, 10, 25, 50, 100]
  return commonValues.map(value => {
    try {
      const result = convertUnit(value, fromUnitId, toUnitId)
      return {
        from: value,
        to: result.result,
        fromFormatted: `${value} ${fromUnitSymbol}`,
        toFormatted: result.formatted
      }
    } catch {
      return {
        from: value,
        to: 0,
        fromFormatted: `${value} ${fromUnitSymbol}`,
        toFormatted: `0 ${toUnitSymbol}`
      }
    }
  })
})()

// Get related conversions
const relatedConversions = computed(() => {
  const popular = getPopularConversions(categoryId)
  // Filter out current conversion and limit to 6
  return popular
    .filter(conv => !(conv.from === fromUnitId && conv.to === toUnitId))
    .slice(0, 6)
})

// Get URL value for SEO (route already defined above)
const urlValue = computed(() => route.query.value as string)

// Calculate result for SEO if value is provided
const seoResult = computed(() => {
  if (!urlValue.value) return null
  try {
    const value = parseFloat(urlValue.value)
    if (isNaN(value)) return null
    return convertUnit(value, fromUnitId, toUnitId)
  } catch {
    return null
  }
})

// Dynamic title with conversion result for SEO
const seoTitle = computed(() => {
  if (seoResult.value) {
    return `${urlValue.value} ${fromUnitSymbol} to ${toUnitSymbol} = ${seoResult.value.formatted} - Convert.land`
  }
  return `${conversionTitle.value} - Convert.land`
})

// Dynamic description with conversion result for SEO
const seoDescription = computed(() => {
  if (seoResult.value) {
    return `${urlValue.value} ${fromUnitName.value} equals ${seoResult.value.formatted}. Convert ${fromUnitName.value.toLowerCase()} to ${toUnitName.value.toLowerCase()} instantly with our free online converter.`
  }
  return `Convert ${fromUnitName.value} to ${toUnitName.value} instantly. Free online ${fromUnitName.value.toLowerCase()} to ${toUnitName.value.toLowerCase()} converter with conversion table and formula.`
})

// SEO
useHead(() => ({
  title: seoTitle.value,
  meta: [
    {
      name: 'description',
      content: seoDescription.value
    },
    {
      name: 'keywords',
      content: `${fromUnitName.value.toLowerCase()} to ${toUnitName.value.toLowerCase()}, ${fromUnitId} to ${toUnitId}, ${t(`categories.${categoryId}`).toLowerCase()} converter, unit conversion`
    }
  ]
}))

// Structured data for SEO
useHead({
  script: [
    {
      type: 'application/ld+json',
      children: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'WebApplication',
        name: conversionTitle.value,
        description: `Convert ${fromUnitName.value} to ${toUnitName.value} instantly`,
        url: `https://convert.land/convert/${categoryId}/${conversionParam}`,
        applicationCategory: 'UtilityApplication',
        operatingSystem: 'Any',
        offers: {
          '@type': 'Offer',
          price: '0',
          priceCurrency: 'USD'
        }
      })
    }
  ]
})
</script>
