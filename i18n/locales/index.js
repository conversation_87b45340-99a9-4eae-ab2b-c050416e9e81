const locales = [
  {
    code: 'en',
    language: 'en-US',
    file: 'en.json',
    name: 'English'
  },
  {
    code: 'zh',
    language: 'zh-CN',
    file: 'zh.json',
    name: '简体中文'
  },
  {
    code: 'es',
    language: 'es-ES',
    file: 'es.json',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>'
  },
  {
    code: 'fr',
    language: 'fr-FR',
    file: 'fr.json',
    name: 'Français'
  },
  {
    code: 'de',
    language: 'de-DE',
    file: 'de.json',
    name: '<PERSON><PERSON><PERSON>'
  },
  {
    code: 'ja',
    language: 'ja-<PERSON>',
    file: 'ja.json',
    name: '日本語'
  },
  {
    code: 'ko',
    language: 'ko-KR',
    file: 'ko.json',
    name: '한국어'
  },
  {
    code: 'pt',
    language: 'pt-BR',
    file: 'pt.json',
    name: 'Português'
  },
  {
    code: 'ru',
    language: 'ru-RU',
    file: 'ru.json',
    name: 'Русский'
  },
  {
    code: 'ar',
    language: 'ar-SA',
    file: 'ar.json',
    name: 'العربية'
  }
]

export default locales