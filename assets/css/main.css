@import "tailwindcss";

/* Mobile performance optimizations */
.touch-manipulation {
  touch-action: manipulation;
}

/* Smooth scrolling for mobile */
html {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Optimize text rendering */
body {
  text-rendering: optimizeSpeed;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Mobile-friendly hover states */
@media (hover: hover) {
  .hover\:shadow-lg:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
}

/* Prevent zoom on input focus on iOS */
@media screen and (max-width: 767px) {
  input[type="number"],
  input[type="text"],
  select {
    font-size: 16px;
  }
}

/* Optimize animations for mobile */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Loading spinner optimization */
.loading-spinner {
  animation: spin 1s linear infinite;
  will-change: transform;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Layout optimization for mobile */
.mobile-optimized {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000;
  perspective: 1000;
}