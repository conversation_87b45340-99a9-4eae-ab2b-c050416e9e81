import { unitCategories, getPopularConversions } from './converter'

export interface SEORoute {
  path: string
  title: string
  description: string
  keywords: string[]
  priority: number
}

/**
 * Generate SEO-optimized routes for popular conversions
 */
export function generateSEORoutes(): SEORoute[] {
  const routes: SEORoute[] = []

  // Add category routes
  unitCategories.forEach(category => {
    routes.push({
      path: `/convert/${category.id}`,
      title: `${category.name} Converter - Convert ${category.name} Units Online`,
      description: `Free online ${category.name.toLowerCase()} converter. ${category.description}. Convert between ${category.units.length} different ${category.name.toLowerCase()} units instantly.`,
      keywords: [
        `${category.name.toLowerCase()} converter`,
        `convert ${category.name.toLowerCase()}`,
        ...category.units.map(unit => unit.name.toLowerCase()),
        'unit converter',
        'online converter',
        'free converter'
      ],
      priority: 0.8
    })

    // Add popular conversion routes for each category
    const popularConversions = getPopularConversions(category.id)
    popularConversions.forEach(conversion => {
      const fromUnit = category.units.find(u => u.id === conversion.from)
      const toUnit = category.units.find(u => u.id === conversion.to)
      
      if (fromUnit && toUnit) {
        routes.push({
          path: `/convert/${category.id}/${conversion.from}-to-${conversion.to}`,
          title: `${fromUnit.name} to ${toUnit.name} Converter - ${conversion.label}`,
          description: `Convert ${fromUnit.name.toLowerCase()} to ${toUnit.name.toLowerCase()} instantly. Free online ${fromUnit.name.toLowerCase()} to ${toUnit.name.toLowerCase()} converter with formula and conversion table.`,
          keywords: [
            `${fromUnit.name.toLowerCase()} to ${toUnit.name.toLowerCase()}`,
            `${conversion.from} to ${conversion.to}`,
            `convert ${fromUnit.name.toLowerCase()}`,
            `${fromUnit.symbol} to ${toUnit.symbol}`,
            `${category.name.toLowerCase()} converter`,
            conversion.label.toLowerCase()
          ],
          priority: 0.9
        })
      }
    })
  })

  // Add specific measurement routes
  const specificRoutes = generateSpecificMeasurementRoutes()
  routes.push(...specificRoutes)

  return routes.sort((a, b) => b.priority - a.priority)
}

/**
 * Generate routes for specific common measurements
 */
function generateSpecificMeasurementRoutes(): SEORoute[] {
  return [
    // Length conversions
    {
      path: '/meters-to-feet',
      title: 'Meters to Feet Converter - m to ft Conversion',
      description: 'Convert meters to feet instantly. 1 meter = 3.28084 feet. Free online meters to feet converter with conversion table and formula.',
      keywords: ['meters to feet', 'm to ft', 'meter feet conversion', 'length converter'],
      priority: 1.0
    },
    {
      path: '/feet-to-meters',
      title: 'Feet to Meters Converter - ft to m Conversion',
      description: 'Convert feet to meters instantly. 1 foot = 0.3048 meters. Free online feet to meters converter with conversion table and formula.',
      keywords: ['feet to meters', 'ft to m', 'foot meter conversion', 'length converter'],
      priority: 1.0
    },
    {
      path: '/inches-to-cm',
      title: 'Inches to Centimeters Converter - in to cm Conversion',
      description: 'Convert inches to centimeters instantly. 1 inch = 2.54 centimeters. Free online inches to cm converter.',
      keywords: ['inches to cm', 'in to cm', 'inch centimeter conversion'],
      priority: 1.0
    },
    {
      path: '/cm-to-inches',
      title: 'Centimeters to Inches Converter - cm to in Conversion',
      description: 'Convert centimeters to inches instantly. 1 cm = 0.393701 inches. Free online cm to inches converter.',
      keywords: ['cm to inches', 'centimeters to inches', 'cm in conversion'],
      priority: 1.0
    },
    
    // Weight conversions
    {
      path: '/kg-to-pounds',
      title: 'Kilograms to Pounds Converter - kg to lbs Conversion',
      description: 'Convert kilograms to pounds instantly. 1 kg = 2.20462 pounds. Free online kg to lbs converter.',
      keywords: ['kg to pounds', 'kilograms to lbs', 'weight converter'],
      priority: 1.0
    },
    {
      path: '/pounds-to-kg',
      title: 'Pounds to Kilograms Converter - lbs to kg Conversion',
      description: 'Convert pounds to kilograms instantly. 1 pound = 0.453592 kg. Free online pounds to kg converter.',
      keywords: ['pounds to kg', 'lbs to kg', 'weight converter'],
      priority: 1.0
    },
    
    // Temperature conversions
    {
      path: '/celsius-to-fahrenheit',
      title: 'Celsius to Fahrenheit Converter - °C to °F Conversion',
      description: 'Convert Celsius to Fahrenheit instantly. Formula: °F = (°C × 9/5) + 32. Free online temperature converter.',
      keywords: ['celsius to fahrenheit', 'c to f', 'temperature converter'],
      priority: 1.0
    },
    {
      path: '/fahrenheit-to-celsius',
      title: 'Fahrenheit to Celsius Converter - °F to °C Conversion',
      description: 'Convert Fahrenheit to Celsius instantly. Formula: °C = (°F - 32) × 5/9. Free online temperature converter.',
      keywords: ['fahrenheit to celsius', 'f to c', 'temperature converter'],
      priority: 1.0
    }
  ]
}

/**
 * Get sitemap URLs
 */
export function getSitemapUrls(baseUrl: string = 'https://convert.land'): Array<{
  url: string
  lastmod: string
  changefreq: string
  priority: number
}> {
  const routes = generateSEORoutes()
  const lastmod = new Date().toISOString().split('T')[0]
  
  return routes.map(route => ({
    url: `${baseUrl}${route.path}`,
    lastmod,
    changefreq: 'weekly',
    priority: route.priority
  }))
}

/**
 * Generate meta tags for a specific route
 */
export function getRouteMeta(path: string) {
  const routes = generateSEORoutes()
  const route = routes.find(r => r.path === path)
  
  if (!route) {
    return null
  }
  
  return {
    title: route.title,
    meta: [
      { name: 'description', content: route.description },
      { name: 'keywords', content: route.keywords.join(', ') },
      { property: 'og:title', content: route.title },
      { property: 'og:description', content: route.description },
      { property: 'og:type', content: 'website' },
      { name: 'twitter:card', content: 'summary' },
      { name: 'twitter:title', content: route.title },
      { name: 'twitter:description', content: route.description }
    ]
  }
}