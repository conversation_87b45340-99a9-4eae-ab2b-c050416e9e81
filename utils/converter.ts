import convert from 'convert'

// Define unit categories and their supported units
export interface UnitCategory {
  id: string
  name: string
  description: string
  icon: string
  units: UnitInfo[]
}

export interface UnitInfo {
  id: string
  name: string
  symbol: string
  description?: string
}

export interface ConversionResult {
  value: number
  fromUnit: string
  toUnit: string
  result: number
  formatted: string
}

// Unit categories configuration
export const unitCategories: UnitCategory[] = [
  {
    id: 'length',
    name: 'Length',
    description: 'Convert between different units of length and distance',
    icon: '📏',
    units: [
      { id: 'mm', name: 'Millimeter', symbol: 'mm' },
      { id: 'cm', name: 'Centimeter', symbol: 'cm' },
      { id: 'm', name: 'Meter', symbol: 'm' },
      { id: 'km', name: 'Kilometer', symbol: 'km' },
      { id: 'in', name: 'Inch', symbol: 'in' },
      { id: 'ft', name: 'Foot', symbol: 'ft' },
      { id: 'yd', name: 'Yard', symbol: 'yd' },
      { id: 'mi', name: 'Mile', symbol: 'mi' }
    ]
  },
  {
    id: 'mass',
    name: 'Weight & Mass',
    description: 'Convert between different units of weight and mass',
    icon: '⚖️',
    units: [
      { id: 'mg', name: 'Milligram', symbol: 'mg' },
      { id: 'g', name: 'Gram', symbol: 'g' },
      { id: 'kg', name: 'Kilogram', symbol: 'kg' },
      { id: 'oz', name: 'Ounce', symbol: 'oz' },
      { id: 'lb', name: 'Pound', symbol: 'lb' },
      { id: 't', name: 'Metric Ton', symbol: 't' }
    ]
  },
  {
    id: 'temperature',
    name: 'Temperature',
    description: 'Convert between Celsius, Fahrenheit, and Kelvin',
    icon: '🌡️',
    units: [
      { id: 'celsius', name: 'Celsius', symbol: '°C' },
      { id: 'fahrenheit', name: 'Fahrenheit', symbol: '°F' },
      { id: 'kelvin', name: 'Kelvin', symbol: 'K' }
    ]
  },
  {
    id: 'area',
    name: 'Area',
    description: 'Convert between different units of area',
    icon: '📐',
    units: [
      { id: 'mm2', name: 'Square Millimeter', symbol: 'mm²' },
      { id: 'cm2', name: 'Square Centimeter', symbol: 'cm²' },
      { id: 'm2', name: 'Square Meter', symbol: 'm²' },
      { id: 'km2', name: 'Square Kilometer', symbol: 'km²' },
      { id: 'in2', name: 'Square Inch', symbol: 'in²' },
      { id: 'ft2', name: 'Square Foot', symbol: 'ft²' },
      { id: 'ac', name: 'Acre', symbol: 'ac' }
    ]
  },
  {
    id: 'volume',
    name: 'Volume',
    description: 'Convert between different units of volume',
    icon: '🥤',
    units: [
      { id: 'ml', name: 'Milliliter', symbol: 'ml' },
      { id: 'l', name: 'Liter', symbol: 'l' },
      { id: 'gal', name: 'Gallon', symbol: 'gal' },
      { id: 'qt', name: 'Quart', symbol: 'qt' },
      { id: 'pt', name: 'Pint', symbol: 'pt' },
      { id: 'cup', name: 'Cup', symbol: 'cup' },
      { id: 'fl-oz', name: 'Fluid Ounce', symbol: 'fl oz' }
    ]
  },
  {
    id: 'time',
    name: 'Time',
    description: 'Convert between different units of time',
    icon: '⏰',
    units: [
      { id: 'ms', name: 'Millisecond', symbol: 'ms' },
      { id: 's', name: 'Second', symbol: 's' },
      { id: 'min', name: 'Minute', symbol: 'min' },
      { id: 'h', name: 'Hour', symbol: 'h' },
      { id: 'd', name: 'Day', symbol: 'd' },
      { id: 'week', name: 'Week', symbol: 'week' },
      { id: 'month', name: 'Month', symbol: 'month' },
      { id: 'year', name: 'Year', symbol: 'year' }
    ]
  },
  {
    id: 'data',
    name: 'Data Storage',
    description: 'Convert between different units of digital storage',
    icon: '💾',
    units: [
      { id: 'b', name: 'Bit', symbol: 'bit' },
      { id: 'B', name: 'Byte', symbol: 'B' },
      { id: 'KB', name: 'Kilobyte', symbol: 'KB' },
      { id: 'MB', name: 'Megabyte', symbol: 'MB' },
      { id: 'GB', name: 'Gigabyte', symbol: 'GB' },
      { id: 'TB', name: 'Terabyte', symbol: 'TB' }
    ]
  },
  {
    id: 'energy',
    name: 'Energy',
    description: 'Convert between different units of energy',
    icon: '⚡',
    units: [
      { id: 'J', name: 'Joule', symbol: 'J' },
      { id: 'kJ', name: 'Kilojoule', symbol: 'kJ' },
      { id: 'kWh', name: 'Kilowatt Hour', symbol: 'kWh' },
      { id: 'cal', name: 'Calorie', symbol: 'cal' },
      { id: 'kcal', name: 'Kilocalorie', symbol: 'kcal' }
    ]
  },
  {
    id: 'power',
    name: 'Power',
    description: 'Convert between different units of power',
    icon: '🔌',
    units: [
      { id: 'W', name: 'Watt', symbol: 'W' },
      { id: 'kW', name: 'Kilowatt', symbol: 'kW' },
      { id: 'MW', name: 'Megawatt', symbol: 'MW' },
      { id: 'hp', name: 'Horsepower', symbol: 'hp' }
    ]
  }
]

// Conversion function
export function convertUnit(value: number, fromUnit: string, toUnit: string): ConversionResult {
  try {
    const result = convert(value, fromUnit as any).to(toUnit as any)
    
    return {
      value,
      fromUnit,
      toUnit,
      result,
      formatted: formatResult(result, toUnit)
    }
  } catch (error) {
    throw new Error(`Cannot convert from ${fromUnit} to ${toUnit}`)
  }
}

// Format result with appropriate precision
export function formatResult(value: number, unit: string): string {
  let precision = 6
  
  // Adjust precision based on value magnitude
  if (Math.abs(value) >= 1000000) {
    precision = 2
  } else if (Math.abs(value) >= 1000) {
    precision = 3
  } else if (Math.abs(value) >= 1) {
    precision = 4
  } else if (Math.abs(value) >= 0.001) {
    precision = 6
  } else {
    precision = 8
  }
  
  // Remove trailing zeros
  const formatted = parseFloat(value.toPrecision(precision)).toString()
  
  return `${formatted} ${getUnitSymbol(unit)}`
}

// Get unit symbol by unit ID
export function getUnitSymbol(unitId: string): string {
  for (const category of unitCategories) {
    const unit = category.units.find(u => u.id === unitId)
    if (unit) {
      return unit.symbol
    }
  }
  return unitId
}

// Get unit name by unit ID
export function getUnitName(unitId: string): string {
  for (const category of unitCategories) {
    const unit = category.units.find(u => u.id === unitId)
    if (unit) {
      return unit.name
    }
  }
  return unitId
}

// Get category by unit ID
export function getCategoryByUnit(unitId: string): UnitCategory | undefined {
  return unitCategories.find(category => 
    category.units.some(unit => unit.id === unitId)
  )
}

// Get popular conversions for a category
export function getPopularConversions(categoryId: string): Array<{from: string, to: string, label: string}> {
  const popularConversions: Record<string, Array<{from: string, to: string, label: string}>> = {
    length: [
      { from: 'ft', to: 'm', label: 'Feet to Meters' },
      { from: 'in', to: 'cm', label: 'Inches to Centimeters' },
      { from: 'km', to: 'mi', label: 'Kilometers to Miles' },
      { from: 'mi', to: 'km', label: 'Miles to Kilometers' }
    ],
    mass: [
      { from: 'lb', to: 'kg', label: 'Pounds to Kilograms' },
      { from: 'kg', to: 'lb', label: 'Kilograms to Pounds' },
      { from: 'oz', to: 'g', label: 'Ounces to Grams' },
      { from: 'g', to: 'oz', label: 'Grams to Ounces' }
    ],
    temperature: [
      { from: 'celsius', to: 'fahrenheit', label: 'Celsius to Fahrenheit' },
      { from: 'fahrenheit', to: 'celsius', label: 'Fahrenheit to Celsius' },
      { from: 'celsius', to: 'kelvin', label: 'Celsius to Kelvin' },
      { from: 'kelvin', to: 'celsius', label: 'Kelvin to Celsius' }
    ],
    volume: [
      { from: 'l', to: 'gal', label: 'Liters to Gallons' },
      { from: 'gal', to: 'l', label: 'Gallons to Liters' },
      { from: 'ml', to: 'fl-oz', label: 'Milliliters to Fluid Ounces' },
      { from: 'cup', to: 'ml', label: 'Cups to Milliliters' }
    ],
    data: [
      { from: 'GB', to: 'MB', label: 'Gigabytes to Megabytes' },
      { from: 'MB', to: 'KB', label: 'Megabytes to Kilobytes' },
      { from: 'TB', to: 'GB', label: 'Terabytes to Gigabytes' },
      { from: 'B', to: 'KB', label: 'Bytes to Kilobytes' }
    ]
  }
  
  return popularConversions[categoryId] || []
}
