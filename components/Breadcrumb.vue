<template>
  <nav class="flex items-center space-x-4 text-sm" aria-label="Breadcrumb">
    <ol class="flex items-center space-x-4">
      <li v-for="(item, index) in breadcrumbs" :key="item.path" class="flex items-center">
        <!-- Home icon for first item -->
        <svg 
          v-if="index === 0" 
          class="w-4 h-4 mr-2 text-gray-400" 
          fill="currentColor" 
          viewBox="0 0 20 20">
          <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
        </svg>
        
        <!-- Breadcrumb item -->
        <NuxtLink 
          v-if="!item.current"
          :to="item.path" 
          class="text-blue-600 hover:text-blue-800 transition-colors"
          :aria-label="`Navigate to ${item.name}`">
          {{ item.name }}
        </NuxtLink>
        <span 
          v-else
          class="text-gray-600 font-medium"
          :aria-current="item.current ? 'page' : undefined">
          {{ item.name }}
        </span>
        
        <!-- Separator -->
        <svg 
          v-if="index < breadcrumbs.length - 1"
          class="w-4 h-4 mx-2 text-gray-400" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </li>
    </ol>
  </nav>
</template>

<script setup lang="ts">
interface BreadcrumbItem {
  name: string
  path: string
  current?: boolean
}

interface Props {
  items: BreadcrumbItem[]
}

const props = defineProps<Props>()

// Ensure the last item is marked as current
const breadcrumbs = computed(() => {
  return props.items.map((item, index) => ({
    ...item,
    current: index === props.items.length - 1
  }))
})
</script>