<template>
  <footer class="bg-gray-900 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <div class="col-span-1 md:col-span-2">
          <h4 class="text-lg font-semibold mb-4">Convert.land</h4>
          <p class="text-gray-400 mb-4">
            The most comprehensive and user-friendly unit converter on the web.
            Convert between hundreds of units across multiple categories.
          </p>
        </div>
        <div>
          <h5 class="font-semibold mb-4">Categories</h5>
          <ul class="space-y-2 text-sm text-gray-400">
            <li v-for="category in unitCategories.slice(0, 4)" :key="category.id">
              <NuxtLink :to="`/convert/${category.id}`" class="hover:text-white">
                {{ $t(`categories.${category.id}`) }}
              </NuxtLink>
            </li>
          </ul>
        </div>
        <div>
          <h5 class="font-semibold mb-4">More Categories</h5>
          <ul class="space-y-2 text-sm text-gray-400">
            <li v-for="category in unitCategories.slice(4)" :key="category.id">
              <NuxtLink :to="`/convert/${category.id}`" class="hover:text-white">
                {{ $t(`categories.${category.id}`) }}
              </NuxtLink>
            </li>
          </ul>
        </div>
      </div>
      <div class="border-t border-gray-800 mt-8 pt-8 text-center text-sm text-gray-400">
        <p>&copy; 2024 Convert.land. All rights reserved.</p>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { unitCategories } from '~/utils/converter'

const { t } = useI18n()
</script>