<template>
  <div class="space-y-6">
    <!-- Main Conversion Interface -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- From Unit -->
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            {{ $t(`units.${fromUnit}`) }} ({{ fromUnitSymbol }})
          </label>
          <input
            v-model="inputValue"
            @input="onInputChange"
            type="number"
            step="any"
            :placeholder="$t('converter.enterValue')"
            class="w-full px-4 py-3 text-lg border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
        </div>
      </div>

      <!-- To Unit -->
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            {{ $t(`units.${toUnit}`) }} ({{ toUnitSymbol }})
          </label>
          <input
            :value="result?.formatted || ''"
            readonly
            :placeholder="$t('converter.result')"
            class="w-full px-4 py-3 text-lg border border-gray-300 rounded-md bg-gray-50 text-gray-900 font-semibold">
        </div>
      </div>
    </div>

    <!-- Swap Button -->
    <div class="flex justify-center">
      <NuxtLink
        :to="`/convert/${category.id}/${toUnit}-to-${fromUnit}`"
        class="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
        </svg>
        <span>{{ $t('converter.convertInstantly', { from: $t(`units.${toUnit}`), to: $t(`units.${fromUnit}`) }) }}</span>
      </NuxtLink>
    </div>

    <!-- Error Message -->
    <div v-if="error" class="bg-red-50 border border-red-200 rounded-md p-4">
      <div class="flex">
        <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
        </svg>
        <span class="text-red-700 text-sm">{{ error }}</span>
      </div>
    </div>

    <!-- Conversion Result -->
    <div v-if="result && !error" class="bg-blue-50 border border-blue-200 rounded-md p-4">
      <div class="flex items-start">
        <svg class="w-5 h-5 text-blue-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
        </svg>
        <div class="text-blue-700">
          <div class="font-medium text-lg">
            {{ inputValue }} {{ fromUnitSymbol }} = {{ result.formatted }}
          </div>
          <div class="text-sm mt-1">
            {{ $t('converter.conversionFactor') }} 1 {{ fromUnitSymbol }} = {{ conversionFactor }} {{ toUnitSymbol }}
          </div>
        </div>
      </div>
    </div>

    <!-- Step-by-step Calculation -->
    <div v-if="result && !error && showCalculation" class="bg-gray-50 border border-gray-200 rounded-md p-4">
      <h3 class="font-medium text-gray-900 mb-3">{{ $t('converter.stepByStepCalculation') }}</h3>
      <div class="space-y-2 text-sm text-gray-700">
        <div>1. {{ $t('converter.startWithValue', { unit: $t(`units.${fromUnit}`), value: inputValue, symbol: fromUnitSymbol }) }}</div>
        <div>2. {{ $t('converter.multiplyByFactor', { value: inputValue, factor: conversionFactor }) }}</div>
        <div>3. {{ $t('converter.resultIs', { result: result.result, symbol: toUnitSymbol }) }}</div>
      </div>
    </div>

    <!-- Toggle Calculation -->
    <div class="flex justify-center">
      <button
        @click="showCalculation = !showCalculation"
        class="text-sm text-blue-600 hover:text-blue-800 underline">
        {{ showCalculation ? $t('converter.hideCalculation') : $t('converter.showCalculation') }}
      </button>
    </div>

    <!-- Quick Preset Values -->
    <div class="bg-white border border-gray-200 rounded-lg p-4">
      <h3 class="text-lg font-medium text-gray-900 mb-4">
        {{ $t('converter.quickValues', { unit: $t(`units.${fromUnit}`) }) }}
      </h3>
      <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2">
        <button
          v-for="preset in presetValues"
          :key="preset"
          @click="setPresetValue(preset)"
          class="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded border transition-colors">
          {{ preset }} {{ fromUnitSymbol }}
        </button>
      </div>
    </div>

    <!-- History -->
    <div v-if="history.length > 0" class="bg-white border border-gray-200 rounded-lg p-4">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">{{ $t('converter.recentConversions') }}</h3>
        <button
          @click="clearHistory"
          class="text-sm text-gray-500 hover:text-gray-700">
          {{ $t('converter.clear') }}
        </button>
      </div>
      <div class="space-y-2">
        <div 
          v-for="(item, index) in history.slice(0, 5)" 
          :key="index"
          @click="loadFromHistory(item)"
          class="flex items-center justify-between p-2 bg-gray-50 rounded cursor-pointer hover:bg-gray-100 transition-colors">
          <span class="text-sm text-gray-700">
            {{ item.value }} {{ fromUnitSymbol }} → {{ item.formatted }}
          </span>
          <span class="text-xs text-gray-500">{{ formatTime(item.timestamp) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { convertUnit, getUnitName, getUnitSymbol, type ConversionResult, type UnitCategory } from '~/utils/converter'

const { t } = useI18n()

// Props
const props = defineProps<{
  category: UnitCategory
  fromUnit: string
  toUnit: string
}>()

// Reactive state
const inputValue = ref('1')
const result = ref<ConversionResult | null>(null)
const error = ref('')
const showCalculation = ref(false)
const history = ref<Array<ConversionResult & { timestamp: number }>>([])

// Computed properties
const fromUnitInfo = computed(() => props.category.units.find(u => u.id === props.fromUnit))
const toUnitInfo = computed(() => props.category.units.find(u => u.id === props.toUnit))
const fromUnitName = computed(() => t(`units.${props.fromUnit}`))
const toUnitName = computed(() => t(`units.${props.toUnit}`))
const fromUnitSymbol = computed(() => fromUnitInfo.value?.symbol || props.fromUnit)
const toUnitSymbol = computed(() => toUnitInfo.value?.symbol || props.toUnit)

const conversionFactor = computed(() => {
  try {
    const factorResult = convertUnit(1, props.fromUnit, props.toUnit)
    return parseFloat(factorResult.result.toPrecision(6))
  } catch {
    return 1
  }
})

// Preset values for quick conversion
const presetValues = [0.1, 0.5, 1, 2, 5, 10, 25, 50, 100, 500, 1000]

// Initialize
onMounted(() => {
  loadHistory()
  
  // Check URL parameters
  const route = useRoute()
  if (route.query.value) {
    inputValue.value = route.query.value as string
  }
  
  onConvert()
})

// Methods
function onInputChange() {
  onConvert()
  updateURL()
}

function onConvert() {
  if (!inputValue.value) {
    result.value = null
    error.value = ''
    return
  }

  try {
    const value = parseFloat(inputValue.value)
    if (isNaN(value)) {
      error.value = t('converter.pleaseEnterValidNumber')
      result.value = null
      return
    }

    result.value = convertUnit(value, props.fromUnit, props.toUnit)
    error.value = ''
    
    // Add to history
    addToHistory(result.value)
  } catch (err) {
    error.value = err instanceof Error ? err.message : t('converter.conversionError')
    result.value = null
  }
}

function setPresetValue(value: number) {
  inputValue.value = value.toString()
  onConvert()
  updateURL()
}

function addToHistory(conversion: ConversionResult) {
  const historyItem = {
    ...conversion,
    timestamp: Date.now()
  }
  
  // Add to beginning of array
  history.value.unshift(historyItem)
  
  // Keep only last 10 items
  history.value = history.value.slice(0, 10)
  
  // Save to localStorage
  saveHistory()
}

function loadFromHistory(item: ConversionResult & { timestamp: number }) {
  inputValue.value = item.value.toString()
  onConvert()
  updateURL()
}

function clearHistory() {
  history.value = []
  saveHistory()
}

function loadHistory() {
  try {
    const saved = localStorage.getItem(`convert-history-${props.fromUnit}-${props.toUnit}`)
    if (saved) {
      history.value = JSON.parse(saved)
    }
  } catch {
    // Ignore localStorage errors
  }
}

function saveHistory() {
  try {
    localStorage.setItem(`convert-history-${props.fromUnit}-${props.toUnit}`, JSON.stringify(history.value))
  } catch {
    // Ignore localStorage errors
  }
}

function updateURL() {
  const router = useRouter()
  const route = useRoute()
  
  router.replace({
    query: {
      ...route.query,
      value: inputValue.value
    }
  })
}

function formatTime(timestamp: number): string {
  const now = Date.now()
  const diff = now - timestamp

  if (diff < 60000) return t('converter.justNow')
  if (diff < 3600000) return t('converter.minutesAgo', { minutes: Math.floor(diff / 60000) })
  if (diff < 86400000) return t('converter.hoursAgo', { hours: Math.floor(diff / 3600000) })
  return t('converter.daysAgo', { days: Math.floor(diff / 86400000) })
}
</script>
