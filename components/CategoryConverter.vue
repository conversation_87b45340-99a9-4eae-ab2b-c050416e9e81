<template>
  <div class="space-y-6">
    <!-- Main Conversion Interface -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- From Unit -->
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">From</label>
          <select 
            v-model="fromUnit"
            @change="onConvert"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option v-for="unit in category.units" :key="unit.id" :value="unit.id">
              {{ unit.name }} ({{ unit.symbol }})
            </option>
          </select>
        </div>
        <div>
          <input
            v-model="inputValue"
            @input="onInputChange"
            type="number"
            step="any"
            placeholder="Enter value to convert"
            class="w-full px-4 py-3 text-lg border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
        </div>
      </div>

      <!-- To Unit -->
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">To</label>
          <select 
            v-model="toUnit"
            @change="onConvert"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option v-for="unit in category.units" :key="unit.id" :value="unit.id">
              {{ unit.name }} ({{ unit.symbol }})
            </option>
          </select>
        </div>
        <div>
          <input
            :value="result?.formatted || ''"
            readonly
            placeholder="Result will appear here"
            class="w-full px-4 py-3 text-lg border border-gray-300 rounded-md bg-gray-50 text-gray-900 font-semibold">
        </div>
      </div>
    </div>

    <!-- Swap Button -->
    <div class="flex justify-center">
      <button
        @click="swapUnits"
        :disabled="!fromUnit || !toUnit"
        class="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
        </svg>
        <span>Swap Units</span>
      </button>
    </div>

    <!-- Error Message -->
    <div v-if="error" class="bg-red-50 border border-red-200 rounded-md p-4">
      <div class="flex">
        <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
        </svg>
        <span class="text-red-700 text-sm">{{ error }}</span>
      </div>
    </div>

    <!-- Conversion Formula -->
    <div v-if="result && !error" class="bg-blue-50 border border-blue-200 rounded-md p-4">
      <div class="flex items-start">
        <svg class="w-5 h-5 text-blue-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
        </svg>
        <div class="text-blue-700 text-sm">
          <div class="font-medium">Conversion Result:</div>
          <div class="mt-1">
            {{ inputValue }} {{ getUnitSymbol(fromUnit) }} = {{ result.formatted }}
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Conversion Table -->
    <div v-if="fromUnit && toUnit && !error" class="bg-gray-50 rounded-lg p-4">
      <h3 class="text-lg font-medium text-gray-900 mb-4">
        Quick Reference: {{ getUnitName(fromUnit) }} to {{ getUnitName(toUnit) }}
      </h3>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div 
          v-for="value in quickValues" 
          :key="value"
          class="text-center p-3 bg-white rounded border">
          <div class="text-sm text-gray-600">{{ value }} {{ getUnitSymbol(fromUnit) }}</div>
          <div class="text-lg font-semibold text-gray-900">
            {{ formatQuickValue(convertQuickValue(value)) }} {{ getUnitSymbol(toUnit) }}
          </div>
        </div>
      </div>
    </div>

    <!-- History -->
    <div v-if="history.length > 0" class="bg-white border border-gray-200 rounded-lg p-4">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Recent Conversions</h3>
        <button 
          @click="clearHistory"
          class="text-sm text-gray-500 hover:text-gray-700">
          Clear
        </button>
      </div>
      <div class="space-y-2">
        <div 
          v-for="(item, index) in history.slice(0, 5)" 
          :key="index"
          @click="loadFromHistory(item)"
          class="flex items-center justify-between p-2 bg-gray-50 rounded cursor-pointer hover:bg-gray-100 transition-colors">
          <span class="text-sm text-gray-700">
            {{ item.value }} {{ getUnitSymbol(item.fromUnit) }} → {{ item.formatted }}
          </span>
          <span class="text-xs text-gray-500">{{ formatTime(item.timestamp) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { convertUnit, getUnitName, getUnitSymbol, type ConversionResult, type UnitCategory } from '~/utils/converter'

// Props
const props = defineProps<{
  category: UnitCategory
}>()

// Reactive state
const inputValue = ref('1')
const fromUnit = ref('')
const toUnit = ref('')
const result = ref<ConversionResult | null>(null)
const error = ref('')
const history = ref<Array<ConversionResult & { timestamp: number }>>([])

// Quick conversion values
const quickValues = [1, 5, 10, 25, 50, 100]

// Initialize units
onMounted(() => {
  // Set default units
  if (props.category.units.length >= 2) {
    fromUnit.value = props.category.units[0].id
    toUnit.value = props.category.units[1].id
  }
  
  // Load history from localStorage
  loadHistory()
  
  // Check URL parameters
  const route = useRoute()
  if (route.query.from) {
    fromUnit.value = route.query.from as string
  }
  if (route.query.to) {
    toUnit.value = route.query.to as string
  }
  if (route.query.value) {
    inputValue.value = route.query.value as string
  }
  
  onConvert()
})

// Methods
function onInputChange() {
  onConvert()
}

function onConvert() {
  if (!inputValue.value || !fromUnit.value || !toUnit.value) {
    result.value = null
    error.value = ''
    return
  }

  try {
    const value = parseFloat(inputValue.value)
    if (isNaN(value)) {
      error.value = 'Please enter a valid number'
      result.value = null
      return
    }

    result.value = convertUnit(value, fromUnit.value, toUnit.value)
    error.value = ''
    
    // Add to history
    addToHistory(result.value)
    
    // Update URL
    updateURL()
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Conversion error'
    result.value = null
  }
}

function swapUnits() {
  if (!fromUnit.value || !toUnit.value) return
  
  const temp = fromUnit.value
  fromUnit.value = toUnit.value
  toUnit.value = temp
  
  // Update input value with the result
  if (result.value) {
    inputValue.value = result.value.result.toString()
  }
  
  onConvert()
}

function convertQuickValue(value: number): number {
  try {
    return convertUnit(value, fromUnit.value, toUnit.value).result
  } catch {
    return 0
  }
}

function formatQuickValue(value: number): string {
  if (value === 0) return '0'
  
  if (Math.abs(value) >= 1000000) {
    return value.toExponential(2)
  } else if (Math.abs(value) >= 1000) {
    return value.toFixed(1)
  } else if (Math.abs(value) >= 1) {
    return value.toFixed(2)
  } else {
    return value.toFixed(4)
  }
}

function addToHistory(conversion: ConversionResult) {
  const historyItem = {
    ...conversion,
    timestamp: Date.now()
  }
  
  // Add to beginning of array
  history.value.unshift(historyItem)
  
  // Keep only last 20 items
  history.value = history.value.slice(0, 20)
  
  // Save to localStorage
  saveHistory()
}

function loadFromHistory(item: ConversionResult & { timestamp: number }) {
  inputValue.value = item.value.toString()
  fromUnit.value = item.fromUnit
  toUnit.value = item.toUnit
  onConvert()
}

function clearHistory() {
  history.value = []
  saveHistory()
}

function loadHistory() {
  try {
    const saved = localStorage.getItem(`convert-history-${props.category.id}`)
    if (saved) {
      history.value = JSON.parse(saved)
    }
  } catch {
    // Ignore localStorage errors
  }
}

function saveHistory() {
  try {
    localStorage.setItem(`convert-history-${props.category.id}`, JSON.stringify(history.value))
  } catch {
    // Ignore localStorage errors
  }
}

function updateURL() {
  const router = useRouter()
  const route = useRoute()
  
  router.replace({
    query: {
      ...route.query,
      from: fromUnit.value,
      to: toUnit.value,
      value: inputValue.value
    }
  })
}

function formatTime(timestamp: number): string {
  const now = Date.now()
  const diff = now - timestamp
  
  if (diff < 60000) return 'Just now'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`
  return `${Math.floor(diff / 86400000)}d ago`
}
</script>
