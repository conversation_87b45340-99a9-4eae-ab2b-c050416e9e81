<template>
  <div class="space-y-4">
    <!-- Category Selection -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('converter.chooseCategory') }}</label>
      <select
        v-model="selectedCategory"
        @change="onCategoryChange"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
        <option value="">{{ $t('converter.selectCategory') }}</option>
        <option v-for="category in unitCategories" :key="category.id" :value="category.id">
          {{ category.icon }} {{ $t(`categories.${category.id}`) }}
        </option>
      </select>
    </div>

    <!-- Conversion Interface -->
    <div v-if="selectedCategory" class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
      <!-- From Unit -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('converter.from') }}</label>
        <div class="space-y-2">
          <div class="relative">
            <input
              v-model="inputValue"
              @input="onInputChange"
              @keydown.enter="onConvert"
              type="number"
              step="any"
              :placeholder="$t('converter.enterValue')"
              class="w-full px-4 py-3 text-lg border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent touch-manipulation pr-10"
              inputmode="decimal">
            <!-- Clear button -->
            <button
              v-if="inputValue"
              @click="clearInput"
              class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              :aria-label="$t('converter.clear')">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          <select 
            v-model="fromUnit"
            @change="onConvert"
            class="w-full px-4 py-3 text-lg border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent touch-manipulation">
            <option v-for="unit in availableUnits" :key="unit.id" :value="unit.id">
              {{ unit.name }} ({{ unit.symbol }})
            </option>
          </select>
        </div>
      </div>

      <!-- Swap Button -->
      <div class="flex justify-center">
        <button
          @click="swapUnits"
          :disabled="!fromUnit || !toUnit"
          class="p-3 bg-blue-500 text-white rounded-full hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200 touch-manipulation hover:scale-105"
          :aria-label="$t('converter.swapUnits')">
          <svg class="w-5 h-5 transition-transform duration-200" :class="{ 'rotate-180': isSwapping }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
          </svg>
        </button>
      </div>

      <!-- To Unit -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('converter.to') }}</label>
        <div class="space-y-2">
          <div class="relative">
            <input
              :value="result?.formatted || ''"
              readonly
              :placeholder="$t('converter.result')"
              class="w-full px-4 py-3 text-lg border border-gray-300 rounded-md bg-gray-50 text-gray-900 touch-manipulation pr-10">
            <!-- Copy button -->
            <button
              v-if="result"
              @click="copyResult"
              class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              :aria-label="$t('converter.copy')">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
              </svg>
            </button>
          </div>
          <select 
            v-model="toUnit"
            @change="onConvert"
            class="w-full px-4 py-3 text-lg border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent touch-manipulation">
            <option v-for="unit in availableUnits" :key="unit.id" :value="unit.id">
              {{ unit.name }} ({{ unit.symbol }})
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- Success/Error Messages -->
    <div v-if="copySuccess" class="text-green-600 text-sm flex items-center">
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
      </svg>
      {{ $t('converter.copiedToClipboard') }}
    </div>
    <div v-else-if="error" class="text-red-600 text-sm flex items-center">
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      {{ error }}
    </div>

    <!-- Popular Conversions for Selected Category -->
    <div v-if="selectedCategory && popularConversions.length > 0" class="mt-6">
      <h4 class="text-sm font-medium text-gray-700 mb-3">{{ $t('converter.popularConversions') }} {{ $t(`categories.${selectedCategory}`) }}</h4>
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
        <button
          v-for="conversion in popularConversions"
          :key="`${conversion.from}-${conversion.to}`"
          @click="setConversion(conversion.from, conversion.to)"
          class="text-left px-3 py-2 text-sm bg-gray-50 hover:bg-gray-100 rounded-md transition-colors">
          {{ conversion.label }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { unitCategories, convertUnit, getPopularConversions, type ConversionResult } from '~/utils/converter'

// Use performance optimizations and analytics
const { debounce } = useDebounce()
const { trackConversion, trackInteraction } = useAnalytics()

// Reactive state
const selectedCategory = ref('')
const inputValue = ref('')
const fromUnit = ref('')
const toUnit = ref('')
const result = ref<ConversionResult | null>(null)
const error = ref('')
const isSwapping = ref(false)
const copySuccess = ref(false)

// Computed properties
const availableUnits = computed(() => {
  const category = unitCategories.find(c => c.id === selectedCategory.value)
  return category?.units || []
})

const selectedCategoryName = computed(() => {
  const category = unitCategories.find(c => c.id === selectedCategory.value)
  return category?.name || ''
})

const popularConversions = computed(() => {
  return selectedCategory.value ? getPopularConversions(selectedCategory.value) : []
})

// Methods
function onCategoryChange() {
  // Reset units when category changes
  fromUnit.value = ''
  toUnit.value = ''
  result.value = null
  error.value = ''
  
  // Set default units if available
  if (availableUnits.value.length >= 2) {
    fromUnit.value = availableUnits.value[0].id
    toUnit.value = availableUnits.value[1].id
    onConvert()
  }
}

// Debounced conversion for better performance
const debouncedConvert = debounce(() => {
  if (!inputValue.value || !fromUnit.value || !toUnit.value) {
    result.value = null
    error.value = ''
    return
  }

  try {
    const value = parseFloat(inputValue.value)
    if (isNaN(value)) {
      error.value = 'Please enter a valid number'
      result.value = null
      return
    }

    result.value = convertUnit(value, fromUnit.value, toUnit.value)
    error.value = ''
    
    // Track successful conversion
    trackConversion({
      category: selectedCategory.value,
      fromUnit: fromUnit.value,
      toUnit: toUnit.value,
      value: value,
      result: result.value.result
    })
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Conversion error'
    result.value = null
  }
}, 300)

function onInputChange() {
  debouncedConvert()
}

function onConvert() {
  // Immediate conversion for unit changes
  if (!inputValue.value || !fromUnit.value || !toUnit.value) {
    result.value = null
    error.value = ''
    return
  }

  try {
    const value = parseFloat(inputValue.value)
    if (isNaN(value)) {
      error.value = 'Please enter a valid number'
      result.value = null
      return
    }

    result.value = convertUnit(value, fromUnit.value, toUnit.value)
    error.value = ''
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Conversion error'
    result.value = null
  }
}

function swapUnits() {
  if (!fromUnit.value || !toUnit.value) return
  
  // Track interaction
  trackInteraction('swap_units', 'swap_button')
  
  isSwapping.value = true
  setTimeout(() => {
    isSwapping.value = false
  }, 200)
  
  const temp = fromUnit.value
  fromUnit.value = toUnit.value
  toUnit.value = temp
  
  // Update input value with the result
  if (result.value) {
    inputValue.value = result.value.result.toString()
  }
  
  onConvert()
}

function clearInput() {
  inputValue.value = ''
  result.value = null
  error.value = ''
}

async function copyResult() {
  if (!result.value) return
  
  try {
    await navigator.clipboard.writeText(result.value.result.toString())
    copySuccess.value = true
    
    // Track copy interaction
    trackInteraction('copy_result', 'copy_button', result.value.result)
    
    setTimeout(() => {
      copySuccess.value = false
    }, 2000)
  } catch (err) {
    console.error('Failed to copy:', err)
  }
}

function setConversion(from: string, to: string) {
  fromUnit.value = from
  toUnit.value = to
  if (inputValue.value) {
    onConvert()
  } else {
    inputValue.value = '1'
    onConvert()
  }
}

// Watch for URL parameters to set initial state
onMounted(() => {
  const route = useRoute()
  if (route.query.category) {
    selectedCategory.value = route.query.category as string
    nextTick(() => {
      if (route.query.from) {
        fromUnit.value = route.query.from as string
      }
      if (route.query.to) {
        toUnit.value = route.query.to as string
      }
      if (route.query.value) {
        inputValue.value = route.query.value as string
      }
      onConvert()
    })
  } else {
    // Default to Length category
    selectedCategory.value = 'length'
    nextTick(() => {
      onCategoryChange()
    })
  }
})
</script>
